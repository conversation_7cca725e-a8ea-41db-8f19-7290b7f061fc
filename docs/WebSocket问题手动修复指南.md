# WebSocket连接问题手动修复指南

## 🔍 问题描述

**错误信息**: `WebSocket is not connected. Need to call "accept" first.`

**问题原因**: WebSocket状态管理不当，在异常处理时尝试发送消息到未正确初始化的WebSocket连接。

## 🛠️ 手动修复步骤

### 步骤1: 停止服务

```bash
cd /data/agent
./manage_server.sh stop
```

### 步骤2: 备份原文件

```bash
cd /data/agent

# 创建备份目录
mkdir -p backup/websocket_fix_$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="backup/websocket_fix_$(date +%Y%m%d_%H%M%S)"

# 备份需要修改的文件
cp agents/predict_algorith_agent/api/websocket_routes.py $BACKUP_DIR/
cp agents/predict_algorith_agent/network/websocket_manager.py $BACKUP_DIR/

echo "备份完成: $BACKUP_DIR"
```

### 步骤3: 修复WebSocket路由文件

编辑 `agents/predict_algorith_agent/api/websocket_routes.py`：

```bash
vim agents/predict_algorith_agent/api/websocket_routes.py
```

找到第82-95行的异常处理部分，替换为：

```python
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析错误: session_id={session_id}, error={e}")
                # 直接发送错误响应，不通过connection_manager
                try:
                    error_response = {
                        "type": "error",
                        "data": {"error": "消息格式错误，请发送有效的JSON"},
                        "session_id": session_id,
                        "status": "error"
                    }
                    await websocket.send_text(json.dumps(error_response, ensure_ascii=False))
                except Exception as send_error:
                    logger.error(f"发送JSON错误响应失败: session_id={session_id}, error={send_error}")
                    break
            except Exception as e:
                logger.error(f"消息处理异常: session_id={session_id}, error={e}")
                # 检查连接是否仍然有效
                if session_id in connection_manager.active_connections:
                    try:
                        # 直接发送错误响应，避免状态问题
                        error_response = {
                            "type": "error",
                            "data": {"error": f"消息处理失败: {str(e)}"},
                            "session_id": session_id,
                            "status": "error"
                        }
                        await websocket.send_text(json.dumps(error_response, ensure_ascii=False))
                    except Exception as send_error:
                        logger.error(f"发送错误响应失败: session_id={session_id}, error={send_error}")
                        # 连接可能已断开，清理连接
                        connection_manager.disconnect(session_id)
                        break
```

### 步骤4: 修复WebSocket管理器文件

编辑 `agents/predict_algorith_agent/network/websocket_manager.py`：

```bash
vim agents/predict_algorith_agent/network/websocket_manager.py
```

找到 `send_personal_message` 方法（约第213行），在异常处理部分添加更详细的错误检查：

```python
        except Exception as e:
            error_msg = str(e)
            logger.error(f"发送消息失败: session_id={session_id}, error={error_msg}")
            
            # 检查是否是WebSocket状态错误
            if "Need to call \"accept\" first" in error_msg or "WebSocket is not connected" in error_msg:
                logger.warning(f"WebSocket状态错误，清理连接: session_id={session_id}")
            
            # 连接可能已断开，清理连接
            self.disconnect(session_id)
```

找到 `send_error_response` 方法（约第697行），添加异常处理：

```python
    async def send_error_response(self, session_id: str, error_message: str):
        """发送错误响应"""
        try:
            error_response = WebSocketResponse(
                type="error",
                data={"error": error_message},
                session_id=session_id,
                status="error"
            )
            await self.send_personal_message(error_response.dict(), session_id)
        except Exception as e:
            logger.error(f"发送错误响应失败: session_id={session_id}, error={e}")
            # 如果发送失败，清理连接
            self.disconnect(session_id)
```

### 步骤5: 启动服务

```bash
cd /data/agent
./manage_server.sh start

# 等待服务启动
sleep 10

# 检查服务状态
./manage_server.sh status
```

### 步骤6: 测试修复效果

```bash
cd /data/agent

# 测试HTTP健康检查
curl -s http://localhost:8008/health

# 测试WebSocket连接
./manage_server.sh test
```

## 🔧 快速修复脚本

如果您想使用自动化脚本，可以执行：

```bash
cd /data/agent

# 下载并执行修复脚本
chmod +x docs/WebSocket连接问题修复脚本.sh
./docs/WebSocket连接问题修复脚本.sh
```

## 🔍 验证修复

修复完成后，检查以下几点：

1. **服务状态正常**：
```bash
./manage_server.sh status
```

2. **WebSocket连接成功**：
```bash
./manage_server.sh test
```

3. **日志无错误**：
```bash
./manage_server.sh logs | tail -20
```

4. **聊天功能正常**：
   - 连接WebSocket
   - 发送测试消息
   - 检查是否有 "Need to call accept first" 错误

## 🔄 回滚方案

如果修复后出现问题，可以快速回滚：

```bash
cd /data/agent

# 停止服务
./manage_server.sh stop

# 恢复备份文件
BACKUP_DIR=$(ls -1 backup/ | grep websocket_fix | tail -1)
cp backup/$BACKUP_DIR/websocket_routes.py agents/predict_algorith_agent/api/websocket_routes.py
cp backup/$BACKUP_DIR/websocket_manager.py agents/predict_algorith_agent/network/websocket_manager.py

# 重启服务
./manage_server.sh start
```

## 📋 修复原理

**问题根源**：
- WebSocket在异常处理时状态不一致
- `connection_manager.send_error_response()` 调用时WebSocket未正确初始化
- 缺乏WebSocket状态检查机制

**修复方案**：
1. **直接发送错误响应**：绕过connection_manager，直接使用websocket.send_text()
2. **增强状态检查**：在发送消息前检查WebSocket状态
3. **改进异常处理**：捕获WebSocket状态错误并清理连接
4. **连接清理机制**：确保异常时正确清理连接

这样修复后，WebSocket连接应该能够正常处理聊天消息，不再出现 "Need to call accept first" 错误。
