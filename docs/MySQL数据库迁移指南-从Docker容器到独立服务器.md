# MySQL数据库迁移指南：从Docker容器到独立服务器

## 📋 迁移概述

**目标**：将***********服务器上的Python服务从连接本机MySQL Docker容器迁移到***********服务器上的MySQL8独立服务器。

**迁移信息**：
- **源服务器**: *********** (MySQL Docker容器 `mysql_2023`)
- **目标服务器**: *********** (MySQL8 独立服务器)
- **数据库配置**: 账号、密码、端口、数据库名、表结构完全一致

## 🔧 迁移步骤

### 步骤1: 备份当前配置文件

```bash
cd /data/agent

# 创建备份目录
mkdir -p backup/$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="backup/$(date +%Y%m%d_%H%M%S)"

# 备份主要配置文件
cp agents/config.py $BACKUP_DIR/config.py.bak
cp agents/predict_algorith_agent/database/database_manager.py $BACKUP_DIR/database_manager.py.bak

echo "✅ 配置文件已备份到: $BACKUP_DIR"
```

### 步骤2: 停止当前服务

```bash
cd /data/agent

# 停止算法智能体服务
./manage_server.sh stop

# 确认服务已停止
./manage_server.sh status

# 检查端口是否释放
netstat -tlnp | grep :8008
```

### 步骤3: 修改数据库连接配置

#### 3.1 修改主配置文件 `agents/config.py`

```bash
# 编辑配置文件
vim agents/config.py
```

找到MySQL配置部分，修改如下：

```python
# MySQL数据库连接配置 - 迁移到242服务器
MYSQL_HOST = os.getenv("DB_HOST", "***********")  # 修改：从 *********** 改为 ***********
MYSQL_PORT = int(os.getenv("DB_PORT", "3306"))     # 保持不变
MYSQL_USER = os.getenv("DB_USER", "root")          # 保持不变
MYSQL_PASSWORD = os.getenv("DB_PASSWORD", "Spsm2021+")  # 保持不变
MYSQL_DB = os.getenv("DB_NAME", "indusaio_agent")  # 保持不变
MYSQL_CHARSET = "utf8mb4"                          # 保持不变
```

#### 3.2 验证其他可能的配置文件

检查是否有其他文件引用了旧的数据库配置：

```bash
# 搜索可能包含数据库配置的文件
grep -r "***********" agents/ --include="*.py" --include="*.yaml" --include="*.yml" --include="*.json"
grep -r "mysql_2023" agents/ --include="*.py"
grep -r "localhost.*3306" agents/ --include="*.py"
```

### 步骤4: 测试新数据库连接

#### 4.1 创建连接测试脚本

```bash
cat > test_new_mysql_connection.py << 'EOF'
#!/usr/bin/env python3
"""
测试新MySQL服务器连接
目标服务器: ***********
"""
import sys
import os
sys.path.insert(0, '/data/agent')

try:
    import pymysql
    from agents.config import MYSQL_HOST, MYSQL_PORT, MYSQL_USER, MYSQL_PASSWORD, MYSQL_DB
    
    print("🔍 测试新MySQL服务器连接...")
    print(f"   目标服务器: {MYSQL_HOST}:{MYSQL_PORT}")
    print(f"   数据库: {MYSQL_DB}")
    print(f"   用户: {MYSQL_USER}")
    
    # 测试连接
    conn = pymysql.connect(
        host=MYSQL_HOST,
        port=MYSQL_PORT,
        user=MYSQL_USER,
        password=MYSQL_PASSWORD,
        database=MYSQL_DB,
        charset='utf8mb4',
        ssl_disabled=True,
        connect_timeout=10
    )
    
    cursor = conn.cursor()
    
    # 测试基本查询
    cursor.execute("SELECT VERSION()")
    version = cursor.fetchone()
    print(f"✅ MySQL连接成功！")
    print(f"   MySQL版本: {version[0]}")
    
    # 测试数据库和表
    cursor.execute("SHOW DATABASES")
    databases = cursor.fetchall()
    print(f"   可用数据库数量: {len(databases)}")
    
    cursor.execute("USE indusaio_agent")
    cursor.execute("SHOW TABLES")
    tables = cursor.fetchall()
    print(f"   indusaio_agent数据库中的表数量: {len(tables)}")
    
    # 测试关键表是否存在
    key_tables = ['conversations', 'conversation_messages', 'session_states']
    cursor.execute("SHOW TABLES")
    existing_tables = [table[0] for table in cursor.fetchall()]
    
    print(f"   关键表检查:")
    for table in key_tables:
        if table in existing_tables:
            print(f"     ✅ {table} - 存在")
        else:
            print(f"     ❌ {table} - 不存在")
    
    cursor.close()
    conn.close()
    
    print("🎉 新MySQL服务器连接测试成功！")
    
except Exception as e:
    print(f"❌ 新MySQL服务器连接失败: {e}")
    print("请检查:")
    print("  1. ***********服务器MySQL服务是否启动")
    print("  2. 网络连接是否正常")
    print("  3. 防火墙设置")
    print("  4. MySQL用户权限")
    sys.exit(1)
EOF

chmod +x test_new_mysql_connection.py
```

#### 4.2 执行连接测试

```bash
cd /data/agent

# 激活Python环境（如果有）
if [ -f "activate_env.sh" ]; then
    source activate_env.sh
fi

# 执行测试
python test_new_mysql_connection.py
```

### 步骤5: 更新环境变量（可选）

如果使用环境变量管理配置，更新相关文件：

```bash
# 检查是否有.env文件
if [ -f ".env" ]; then
    echo "发现.env文件，更新数据库配置..."
    
    # 备份原文件
    cp .env .env.bak
    
    # 更新数据库主机
    sed -i 's/DB_HOST=***********/DB_HOST=***********/g' .env
    
    echo "✅ .env文件已更新"
fi

# 检查其他可能的环境配置文件
for file in .env.local .env.production .env.development; do
    if [ -f "$file" ]; then
        echo "发现 $file，请手动检查并更新数据库配置"
    fi
done
```

### 步骤6: 启动服务并验证

#### 6.1 启动服务

```bash
cd /data/agent

# 启动算法智能体服务
./manage_server.sh start

# 等待服务启动
sleep 10

# 检查服务状态
./manage_server.sh status
```

#### 6.2 验证服务功能

```bash
# 测试WebSocket连接
./manage_server.sh test

# 检查服务日志
./manage_server.sh logs | tail -20

# 测试HTTP健康检查
curl -s http://localhost:8008/health
```

### 步骤7: 清理和优化

#### 7.1 清理测试文件

```bash
cd /data/agent

# 删除测试脚本
rm -f test_new_mysql_connection.py

echo "✅ 测试文件已清理"
```

#### 7.2 更新文档和注释

```bash
# 在配置文件中添加迁移记录
cat >> agents/config.py << 'EOF'

# 数据库迁移记录
# 迁移日期: $(date +%Y-%m-%d)
# 迁移说明: 从*********** Docker容器迁移到***********独立MySQL服务器
# 迁移原因: 解决Docker容器网络连接问题
EOF
```

## 🔍 故障排查

### 常见问题及解决方案

#### 问题1: 连接被拒绝
```bash
# 检查目标服务器MySQL服务状态
ssh root@*********** "systemctl status mysql"

# 检查端口监听
ssh root@*********** "netstat -tlnp | grep :3306"
```

#### 问题2: 权限不足
```bash
# 在242服务器上创建远程访问权限
ssh root@*********** "mysql -u root -p"
```

在MySQL中执行：
```sql
-- 为241服务器创建访问权限
CREATE USER IF NOT EXISTS 'root'@'***********' IDENTIFIED BY 'Spsm2021+';
GRANT ALL PRIVILEGES ON *.* TO 'root'@'***********' WITH GRANT OPTION;

-- 为通配符创建权限（备用）
GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' WITH GRANT OPTION;

FLUSH PRIVILEGES;
```

#### 问题3: 防火墙阻止
```bash
# 在242服务器上开放MySQL端口
ssh root@*********** "ufw allow from *********** to any port 3306"
# 或者
ssh root@*********** "firewall-cmd --permanent --add-rich-rule='rule family=ipv4 source address=*********** port protocol=tcp port=3306 accept'"
ssh root@*********** "firewall-cmd --reload"
```

## ✅ 迁移验证清单

- [ ] 配置文件已备份
- [ ] 服务已停止
- [ ] 数据库连接配置已更新
- [ ] 新数据库连接测试成功
- [ ] 服务重新启动成功
- [ ] WebSocket连接测试通过
- [ ] 应用功能正常
- [ ] 日志无错误信息

## 📝 回滚方案

如果迁移失败，可以快速回滚：

```bash
cd /data/agent

# 停止服务
./manage_server.sh stop

# 恢复配置文件
BACKUP_DIR=$(ls -1 backup/ | tail -1)
cp backup/$BACKUP_DIR/config.py.bak agents/config.py

# 重启服务
./manage_server.sh start

echo "✅ 已回滚到原始配置"
```

## 🎯 迁移完成后的优势

1. **解决网络连接问题** - 避免Docker容器网络复杂性
2. **提高连接稳定性** - 直接TCP连接更稳定
3. **简化运维管理** - 独立MySQL服务器更易管理
4. **提升性能** - 减少Docker网络层开销

## 🚀 快速执行方案

### 方案1: 一键迁移脚本（推荐）

```bash
cd /data/agent

# 下载并执行一键迁移脚本
chmod +x docs/一键迁移脚本.sh
./docs/一键迁移脚本.sh
```

### 方案2: 手动执行

按照上述详细步骤逐步执行。

## 📋 迁移前检查清单

在执行迁移前，请确认以下条件：

- [ ] **目标服务器MySQL8已安装并运行**
  ```bash
  ssh root@*********** "systemctl status mysql"
  ```

- [ ] **目标服务器数据库和表已创建**
  ```bash
  ssh root@*********** "mysql -u root -p -e 'SHOW DATABASES; USE indusaio_agent; SHOW TABLES;'"
  ```

- [ ] **网络连通性正常**
  ```bash
  ping -c 3 ***********
  telnet *********** 3306
  ```

- [ ] **MySQL用户权限已配置**
  ```sql
  -- 在242服务器上执行
  CREATE USER IF NOT EXISTS 'root'@'***********' IDENTIFIED BY 'Spsm2021+';
  GRANT ALL PRIVILEGES ON *.* TO 'root'@'***********' WITH GRANT OPTION;
  FLUSH PRIVILEGES;
  ```

- [ ] **防火墙规则已配置**
  ```bash
  # 在242服务器上执行
  ufw allow from *********** to any port 3306
  ```

- [ ] **当前服务可以正常停止和启动**
  ```bash
  cd /data/agent
  ./manage_server.sh stop
  ./manage_server.sh start
  ./manage_server.sh status
  ```

---

**迁移完成时间**: 预计15-30分钟
**风险等级**: 低（有完整备份和回滚方案）
**建议执行时间**: 业务低峰期
