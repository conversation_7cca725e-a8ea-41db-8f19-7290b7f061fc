# WebSocket导入错误手动修复指南

## 🔍 问题描述

**错误信息**: `Module 'WebSocketState' not found`

**问题位置**: `agents/predict_algorith_agent/network/websocket_manager.py` 第224行

**问题原因**: 不同版本的FastAPI中 `WebSocketState` 的导入路径不同，导致导入失败。

## 🛠️ 手动修复步骤

### 步骤1: 停止服务

```bash
cd /data/agent
./manage_server.sh stop
```

### 步骤2: 备份文件

```bash
cd /data/agent

# 创建备份
mkdir -p backup/websocket_import_fix_$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="backup/websocket_import_fix_$(date +%Y%m%d_%H%M%S)"
cp agents/predict_algorith_agent/network/websocket_manager.py $BACKUP_DIR/

echo "备份完成: $BACKUP_DIR"
```

### 步骤3: 修复导入错误

编辑文件：
```bash
vim agents/predict_algorith_agent/network/websocket_manager.py
```

找到第221-245行左右的代码段，将：

```python
            # 检查WebSocket连接状态 - 使用更安全的方法
            try:
                # 检查FastAPI WebSocket状态
                from fastapi import WebSocketState
                if hasattr(websocket, 'application_state'):
                    if websocket.application_state == WebSocketState.DISCONNECTED:
                        logger.warning(f"WebSocket应用已断开: session_id={session_id}")
                        self.disconnect(session_id)
                        return
                
                # 检查客户端状态
                if hasattr(websocket, 'client_state'):
                    # 对于某些WebSocket实现，检查client_state
                    if hasattr(websocket.client_state, 'name') and websocket.client_state.name == "DISCONNECTED":
                        logger.warning(f"WebSocket客户端已断开: session_id={session_id}")
                        self.disconnect(session_id)
                        return
                    elif hasattr(websocket.client_state, 'value') and websocket.client_state.value == 3:  # CLOSED
                        logger.warning(f"WebSocket客户端已关闭: session_id={session_id}")
                        self.disconnect(session_id)
                        return
                        
            except Exception as state_check_error:
                # 状态检查失败，但不影响发送尝试
                logger.debug(f"WebSocket状态检查失败，继续尝试发送: {state_check_error}")
```

**替换为**：

```python
            # 检查WebSocket连接状态 - 使用简单可靠的方法
            try:
                # 检查应用状态（数值检查，避免导入问题）
                if hasattr(websocket, 'application_state'):
                    # 3 通常表示 DISCONNECTED 状态
                    if websocket.application_state == 3:
                        logger.warning(f"WebSocket应用已断开: session_id={session_id}")
                        self.disconnect(session_id)
                        return
                
                # 检查客户端状态
                if hasattr(websocket, 'client_state'):
                    # 检查客户端状态值
                    if hasattr(websocket.client_state, 'name') and websocket.client_state.name == "DISCONNECTED":
                        logger.warning(f"WebSocket客户端已断开: session_id={session_id}")
                        self.disconnect(session_id)
                        return
                    elif hasattr(websocket.client_state, 'value') and websocket.client_state.value == 3:  # CLOSED
                        logger.warning(f"WebSocket客户端已关闭: session_id={session_id}")
                        self.disconnect(session_id)
                        return
                        
            except Exception as state_check_error:
                # 状态检查失败，但不影响发送尝试
                logger.debug(f"WebSocket状态检查失败，继续尝试发送: {state_check_error}")
```

### 步骤4: 验证修复

检查语法是否正确：
```bash
cd /data/agent
python3 -m py_compile agents/predict_algorith_agent/network/websocket_manager.py
```

如果没有错误输出，说明语法正确。

### 步骤5: 启动服务

```bash
cd /data/agent
./manage_server.sh start

# 等待服务启动
sleep 10

# 检查服务状态
./manage_server.sh status
```

### 步骤6: 验证修复效果

```bash
cd /data/agent

# 检查服务日志，确认无导入错误
./manage_server.sh logs | tail -20

# 测试WebSocket连接
./manage_server.sh test
```

## 🔧 快速修复脚本

如果您想使用自动化脚本：

```bash
cd /data/agent

# 下载并执行修复脚本
chmod +x docs/WebSocket导入错误修复脚本.sh
./docs/WebSocket导入错误修复脚本.sh
```

## 🔍 修复原理

**问题根源**：
- FastAPI不同版本中 `WebSocketState` 的导入路径不同
- 有些版本在 `fastapi` 模块中，有些在 `fastapi.websockets` 或 `starlette.websockets` 中

**修复方案**：
- 移除对 `WebSocketState` 的导入依赖
- 使用数值常量（3 = DISCONNECTED）进行状态检查
- 保持功能不变，但避免导入问题

## 🔄 回滚方案

如果修复后出现问题：

```bash
cd /data/agent

# 停止服务
./manage_server.sh stop

# 恢复备份文件
BACKUP_DIR=$(ls -1 backup/ | grep websocket_import_fix | tail -1)
cp backup/$BACKUP_DIR/websocket_manager.py agents/predict_algorith_agent/network/websocket_manager.py

# 重启服务
./manage_server.sh start
```

## ✅ 验证清单

修复完成后，确认以下几点：

- [ ] 服务正常启动，无导入错误
- [ ] WebSocket连接测试通过
- [ ] 日志中无 "Module not found" 错误
- [ ] 聊天功能正常工作

这个修复方案避免了复杂的导入兼容性问题，使用简单的数值检查来实现相同的功能。
