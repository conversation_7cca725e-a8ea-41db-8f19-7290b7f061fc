#!/bin/bash
# WebSocket根本问题修复脚本
# 修复 "WebSocket is not connected. Need to call accept first" 的根本问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
PROJECT_DIR="/data/agent"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 检查项目目录
check_project_dir() {
    if [ ! -d "$PROJECT_DIR" ]; then
        log_error "项目目录不存在: $PROJECT_DIR"
        exit 1
    fi
    
    log_success "项目目录检查通过"
}

# 创建备份
create_backup() {
    log_info "创建文件备份..."
    
    cd "$PROJECT_DIR"
    
    BACKUP_DIR="backup/websocket_final_fix_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # 备份需要修改的文件
    cp agents/predict_algorith_agent/network/websocket_manager.py "$BACKUP_DIR/"
    cp agents/predict_algorith_agent/api/websocket_routes.py "$BACKUP_DIR/"
    
    log_success "备份完成: $BACKUP_DIR"
    echo "$BACKUP_DIR" > .websocket_final_fix_backup
}

# 停止服务
stop_service() {
    log_info "停止算法智能体服务..."
    
    cd "$PROJECT_DIR"
    
    if [ -f "manage_server.sh" ]; then
        ./manage_server.sh stop
        sleep 3
        log_success "服务已停止"
    else
        log_warning "管理脚本不存在，手动检查进程..."
        pkill -f "predict_main.py" || true
    fi
}

# 应用最新修复
apply_fixes() {
    log_info "应用WebSocket根本问题修复..."
    
    cd "$PROJECT_DIR"
    
    # 检查文件是否存在
    if [ ! -f "agents/predict_algorith_agent/network/websocket_manager.py" ]; then
        log_error "WebSocket管理器文件不存在"
        return 1
    fi
    
    if [ ! -f "agents/predict_algorith_agent/api/websocket_routes.py" ]; then
        log_error "WebSocket路由文件不存在"
        return 1
    fi
    
    log_success "文件检查通过，修复已在代码中应用"
}

# 验证修复
verify_fixes() {
    log_info "验证修复效果..."
    
    cd "$PROJECT_DIR"
    
    # 检查Python语法
    if python3 -m py_compile agents/predict_algorith_agent/network/websocket_manager.py; then
        log_success "WebSocket管理器语法检查通过"
    else
        log_error "WebSocket管理器语法检查失败"
        return 1
    fi
    
    if python3 -m py_compile agents/predict_algorith_agent/api/websocket_routes.py; then
        log_success "WebSocket路由语法检查通过"
    else
        log_error "WebSocket路由语法检查失败"
        return 1
    fi
}

# 启动服务
start_service() {
    log_info "启动算法智能体服务..."
    
    cd "$PROJECT_DIR"
    
    if [ -f "manage_server.sh" ]; then
        ./manage_server.sh start
        sleep 15  # 给更多时间启动
        
        # 检查服务状态
        if ./manage_server.sh status | grep -q "运行中"; then
            log_success "服务启动成功"
        else
            log_error "服务启动失败"
            return 1
        fi
    else
        log_error "管理脚本不存在"
        return 1
    fi
}

# 测试修复效果
test_fixes() {
    log_info "测试WebSocket修复效果..."
    
    cd "$PROJECT_DIR"
    
    # 等待服务完全启动
    sleep 5
    
    # 测试HTTP健康检查
    if curl -s http://localhost:8008/health | grep -q "ok"; then
        log_success "HTTP健康检查通过"
    else
        log_warning "HTTP健康检查失败"
    fi
    
    # 测试WebSocket连接
    log_info "执行WebSocket连接测试..."
    test_output=$(./manage_server.sh test 2>&1)
    
    echo "测试输出:"
    echo "$test_output"
    
    if echo "$test_output" | grep -q "连接成功\|测试成功\|✅"; then
        if echo "$test_output" | grep -q "Need to call.*accept.*first\|WebSocket is not connected"; then
            log_warning "WebSocket连接成功但仍有状态错误"
            return 1
        else
            log_success "WebSocket连接测试完全通过"
        fi
    else
        log_warning "WebSocket连接测试失败"
        return 1
    fi
    
    # 检查最近的日志
    log_info "检查服务日志..."
    recent_logs=$(./manage_server.sh logs | tail -30)
    
    if echo "$recent_logs" | grep -q "Need to call.*accept.*first\|WebSocket is not connected"; then
        log_warning "日志中仍有WebSocket状态错误"
        echo "相关错误日志:"
        echo "$recent_logs" | grep "Need to call.*accept.*first\|WebSocket is not connected" | tail -5
        return 1
    else
        log_success "日志检查通过，无WebSocket状态错误"
    fi
}

# 回滚函数
rollback() {
    log_warning "执行回滚操作..."
    
    cd "$PROJECT_DIR"
    
    # 停止服务
    if [ -f "manage_server.sh" ]; then
        ./manage_server.sh stop
    fi
    
    # 恢复文件
    if [ -f ".websocket_final_fix_backup" ]; then
        BACKUP_DIR=$(cat .websocket_final_fix_backup)
        if [ -f "$BACKUP_DIR/websocket_manager.py" ]; then
            cp "$BACKUP_DIR/websocket_manager.py" agents/predict_algorith_agent/network/websocket_manager.py
            log_success "WebSocket管理器文件已回滚"
        fi
        
        if [ -f "$BACKUP_DIR/websocket_routes.py" ]; then
            cp "$BACKUP_DIR/websocket_routes.py" agents/predict_algorith_agent/api/websocket_routes.py
            log_success "WebSocket路由文件已回滚"
        fi
    fi
    
    # 重启服务
    if [ -f "manage_server.sh" ]; then
        ./manage_server.sh start
    fi
    
    log_success "回滚完成"
}

# 主函数
main() {
    echo "🔧 WebSocket根本问题修复脚本"
    echo "修复 'WebSocket is not connected. Need to call accept first' 的根本问题"
    echo "=" * 70
    
    # 确认执行
    read -p "确认执行修复？(y/N): " confirm
    if [[ ! $confirm =~ ^[Yy]$ ]]; then
        log_info "修复已取消"
        exit 0
    fi
    
    # 执行修复步骤
    if check_project_dir && \
       create_backup && \
       stop_service && \
       apply_fixes && \
       verify_fixes && \
       start_service && \
       test_fixes; then
        
        log_success "🎉 WebSocket根本问题修复完成！"
        echo ""
        echo "修复摘要:"
        echo "  ✅ 简化了WebSocket状态检查"
        echo "  ✅ 移除了复杂的异常处理循环"
        echo "  ✅ 增加了详细的状态日志"
        echo "  ✅ WebSocket连接和消息发送正常"
        echo ""
        echo "建议:"
        echo "  1. 继续监控服务运行状态"
        echo "  2. 测试完整的聊天功能"
        echo "  3. 观察日志中是否还有状态错误"
        
    else
        log_error "修复过程中出现错误"
        read -p "是否执行回滚？(y/N): " rollback_confirm
        if [[ $rollback_confirm =~ ^[Yy]$ ]]; then
            rollback
        fi
        exit 1
    fi
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
