#!/bin/bash
# WebSocket导入错误修复脚本
# 修复 "Module 'WebSocketState' not found" 错误

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
PROJECT_DIR="/data/agent"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 检查项目目录
check_project_dir() {
    if [ ! -d "$PROJECT_DIR" ]; then
        log_error "项目目录不存在: $PROJECT_DIR"
        exit 1
    fi
    
    if [ ! -f "$PROJECT_DIR/agents/predict_algorith_agent/network/websocket_manager.py" ]; then
        log_error "WebSocket管理器文件不存在"
        exit 1
    fi
    
    log_success "项目目录检查通过"
}

# 创建备份
create_backup() {
    log_info "创建文件备份..."
    
    cd "$PROJECT_DIR"
    
    BACKUP_DIR="backup/websocket_import_fix_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # 备份需要修改的文件
    cp agents/predict_algorith_agent/network/websocket_manager.py "$BACKUP_DIR/"
    
    log_success "备份完成: $BACKUP_DIR"
    echo "$BACKUP_DIR" > .websocket_import_fix_backup
}

# 停止服务
stop_service() {
    log_info "停止算法智能体服务..."
    
    cd "$PROJECT_DIR"
    
    if [ -f "manage_server.sh" ]; then
        ./manage_server.sh stop
        sleep 3
        log_success "服务已停止"
    else
        log_warning "管理脚本不存在，手动检查进程..."
        pkill -f "predict_main.py" || true
    fi
}

# 修复WebSocket导入错误
fix_websocket_import() {
    log_info "修复WebSocket导入错误..."
    
    cd "$PROJECT_DIR"
    
    # 使用sed修复导入问题
    cp agents/predict_algorith_agent/network/websocket_manager.py agents/predict_algorith_agent/network/websocket_manager.py.tmp
    
    # 创建修复后的代码段
    cat > websocket_fix_patch.py << 'EOF'
            # 检查WebSocket连接状态 - 使用简单可靠的方法
            try:
                # 检查应用状态（数值检查，避免导入问题）
                if hasattr(websocket, 'application_state'):
                    # 3 通常表示 DISCONNECTED 状态
                    if websocket.application_state == 3:
                        logger.warning(f"WebSocket应用已断开: session_id={session_id}")
                        self.disconnect(session_id)
                        return
                
                # 检查客户端状态
                if hasattr(websocket, 'client_state'):
                    # 检查客户端状态值
                    if hasattr(websocket.client_state, 'name') and websocket.client_state.name == "DISCONNECTED":
                        logger.warning(f"WebSocket客户端已断开: session_id={session_id}")
                        self.disconnect(session_id)
                        return
                    elif hasattr(websocket.client_state, 'value') and websocket.client_state.value == 3:  # CLOSED
                        logger.warning(f"WebSocket客户端已关闭: session_id={session_id}")
                        self.disconnect(session_id)
                        return
                        
            except Exception as state_check_error:
                # 状态检查失败，但不影响发送尝试
                logger.debug(f"WebSocket状态检查失败，继续尝试发送: {state_check_error}")
EOF
    
    # 使用Python脚本进行精确替换
    python3 << 'PYTHON_SCRIPT'
import re

# 读取原文件
with open('agents/predict_algorith_agent/network/websocket_manager.py', 'r', encoding='utf-8') as f:
    content = f.read()

# 查找并替换有问题的导入部分
# 匹配从 "from fastapi import WebSocketState" 开始到状态检查结束的部分
pattern = r'(\s+)# 检查FastAPI WebSocket状态.*?logger\.debug\(f"WebSocket状态检查失败，继续尝试发送: \{state_check_error\}"\)'

# 读取修复代码
with open('websocket_fix_patch.py', 'r', encoding='utf-8') as f:
    fix_code = f.read()

# 执行替换
new_content = re.sub(pattern, fix_code, content, flags=re.DOTALL)

# 如果没有找到匹配，尝试更简单的模式
if new_content == content:
    # 查找包含 "from fastapi import WebSocketState" 的行
    if "from fastapi import WebSocketState" in content:
        # 简单替换有问题的导入行
        new_content = content.replace(
            "from fastapi import WebSocketState",
            "# WebSocketState导入已修复，使用数值检查"
        )
        print("执行了简单的导入行替换")
    else:
        print("未找到需要修复的导入问题")

# 写入修复后的文件
with open('agents/predict_algorith_agent/network/websocket_manager.py', 'w', encoding='utf-8') as f:
    f.write(new_content)

print("WebSocket导入错误修复完成")
PYTHON_SCRIPT
    
    # 清理临时文件
    rm -f websocket_fix_patch.py
    
    log_success "WebSocket导入错误已修复"
}

# 验证修复
verify_fix() {
    log_info "验证修复效果..."
    
    cd "$PROJECT_DIR"
    
    # 检查语法错误
    if python3 -m py_compile agents/predict_algorith_agent/network/websocket_manager.py; then
        log_success "Python语法检查通过"
    else
        log_error "Python语法检查失败"
        return 1
    fi
    
    # 检查是否还有导入错误
    if grep -q "from fastapi import WebSocketState" agents/predict_algorith_agent/network/websocket_manager.py; then
        log_warning "仍然存在可能有问题的导入"
    else
        log_success "导入问题已修复"
    fi
}

# 启动服务
start_service() {
    log_info "启动算法智能体服务..."
    
    cd "$PROJECT_DIR"
    
    if [ -f "manage_server.sh" ]; then
        ./manage_server.sh start
        sleep 10
        
        # 检查服务状态
        if ./manage_server.sh status | grep -q "运行中"; then
            log_success "服务启动成功"
        else
            log_error "服务启动失败"
            return 1
        fi
    else
        log_error "管理脚本不存在"
        return 1
    fi
}

# 测试修复效果
test_fix() {
    log_info "测试修复效果..."
    
    cd "$PROJECT_DIR"
    
    # 等待服务完全启动
    sleep 5
    
    # 测试HTTP健康检查
    if curl -s http://localhost:8008/health | grep -q "ok"; then
        log_success "HTTP健康检查通过"
    else
        log_warning "HTTP健康检查失败"
    fi
    
    # 检查日志中是否还有导入错误
    if ./manage_server.sh logs | tail -20 | grep -q "Module.*not found"; then
        log_warning "日志中仍有模块导入错误"
    else
        log_success "无模块导入错误"
    fi
}

# 回滚函数
rollback() {
    log_warning "执行回滚操作..."
    
    cd "$PROJECT_DIR"
    
    # 停止服务
    if [ -f "manage_server.sh" ]; then
        ./manage_server.sh stop
    fi
    
    # 恢复文件
    if [ -f ".websocket_import_fix_backup" ]; then
        BACKUP_DIR=$(cat .websocket_import_fix_backup)
        if [ -f "$BACKUP_DIR/websocket_manager.py" ]; then
            cp "$BACKUP_DIR/websocket_manager.py" agents/predict_algorith_agent/network/websocket_manager.py
            log_success "WebSocket管理器文件已回滚"
        fi
    fi
    
    # 重启服务
    if [ -f "manage_server.sh" ]; then
        ./manage_server.sh start
    fi
    
    log_success "回滚完成"
}

# 主函数
main() {
    echo "🔧 WebSocket导入错误修复脚本"
    echo "修复 'Module WebSocketState not found' 错误"
    echo "=" * 60
    
    # 确认执行
    read -p "确认执行修复？(y/N): " confirm
    if [[ ! $confirm =~ ^[Yy]$ ]]; then
        log_info "修复已取消"
        exit 0
    fi
    
    # 执行修复步骤
    if check_project_dir && \
       create_backup && \
       stop_service && \
       fix_websocket_import && \
       verify_fix && \
       start_service && \
       test_fix; then
        
        log_success "🎉 WebSocket导入错误修复完成！"
        echo ""
        echo "修复摘要:"
        echo "  ✅ 修复了WebSocketState导入问题"
        echo "  ✅ 使用数值检查替代导入依赖"
        echo "  ✅ 服务正常启动"
        echo ""
        echo "建议:"
        echo "  1. 测试WebSocket连接功能"
        echo "  2. 监控服务日志"
        echo "  3. 确认无导入错误"
        
    else
        log_error "修复过程中出现错误"
        read -p "是否执行回滚？(y/N): " rollback_confirm
        if [[ $rollback_confirm =~ ^[Yy]$ ]]; then
            rollback
        fi
        exit 1
    fi
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
