#!/bin/bash
# MySQL数据库迁移验证脚本
# 验证从Docker容器到独立服务器的迁移是否成功

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
PROJECT_DIR="/data/agent"
NEW_HOST="***********"
MYSQL_USER="root"
MYSQL_PASSWORD="Spsm2021+"
MYSQL_DB="indusaio_agent"
MYSQL_PORT="3306"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 验证配置文件
verify_config() {
    log_info "验证配置文件..."
    
    cd "$PROJECT_DIR"
    
    if grep -q "$NEW_HOST" agents/config.py; then
        log_success "配置文件已更新为新数据库地址"
    else
        log_error "配置文件未正确更新"
        return 1
    fi
    
    # 检查是否还有旧地址残留
    if grep -q "***********" agents/config.py; then
        log_warning "配置文件中仍有旧地址残留，请检查"
        grep -n "***********" agents/config.py
    fi
}

# 验证数据库连接
verify_database_connection() {
    log_info "验证数据库连接..."
    
    cd "$PROJECT_DIR"
    
    # 创建详细的数据库测试脚本
    cat > verify_db.py << EOF
#!/usr/bin/env python3
import sys
sys.path.insert(0, '$PROJECT_DIR')

try:
    import pymysql
    from agents.config import MYSQL_HOST, MYSQL_PORT, MYSQL_USER, MYSQL_PASSWORD, MYSQL_DB
    
    print(f"🔍 连接信息:")
    print(f"   主机: {MYSQL_HOST}")
    print(f"   端口: {MYSQL_PORT}")
    print(f"   数据库: {MYSQL_DB}")
    print(f"   用户: {MYSQL_USER}")
    print()
    
    # 建立连接
    conn = pymysql.connect(
        host=MYSQL_HOST,
        port=MYSQL_PORT,
        user=MYSQL_USER,
        password=MYSQL_PASSWORD,
        database=MYSQL_DB,
        charset='utf8mb4',
        ssl_disabled=True,
        connect_timeout=10
    )
    
    cursor = conn.cursor()
    
    # 基本信息查询
    cursor.execute("SELECT VERSION(), @@hostname, USER()")
    result = cursor.fetchone()
    print(f"✅ 数据库连接成功!")
    print(f"   MySQL版本: {result[0]}")
    print(f"   服务器主机名: {result[1]}")
    print(f"   当前用户: {result[2]}")
    print()
    
    # 检查数据库
    cursor.execute("SHOW DATABASES")
    databases = [db[0] for db in cursor.fetchall()]
    if MYSQL_DB in databases:
        print(f"✅ 目标数据库 '{MYSQL_DB}' 存在")
    else:
        print(f"❌ 目标数据库 '{MYSQL_DB}' 不存在")
        sys.exit(1)
    
    # 检查表
    cursor.execute(f"USE {MYSQL_DB}")
    cursor.execute("SHOW TABLES")
    tables = [table[0] for table in cursor.fetchall()]
    
    print(f"📋 数据库表信息:")
    print(f"   总表数: {len(tables)}")
    
    # 检查关键表
    key_tables = [
        'conversations',
        'conversation_messages', 
        'session_states',
        'users',
        'projects'
    ]
    
    for table in key_tables:
        if table in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"   ✅ {table}: {count} 条记录")
        else:
            print(f"   ⚠️ {table}: 表不存在")
    
    # 测试写入操作
    try:
        test_table = "migration_test"
        cursor.execute(f"CREATE TEMPORARY TABLE {test_table} (id INT, test_data VARCHAR(100))")
        cursor.execute(f"INSERT INTO {test_table} VALUES (1, 'migration_test_data')")
        cursor.execute(f"SELECT * FROM {test_table}")
        test_result = cursor.fetchone()
        if test_result:
            print(f"✅ 数据库写入测试成功")
        cursor.execute(f"DROP TEMPORARY TABLE {test_table}")
    except Exception as e:
        print(f"⚠️ 数据库写入测试失败: {e}")
    
    cursor.close()
    conn.close()
    
    print()
    print("🎉 数据库验证完成!")
    
except Exception as e:
    print(f"❌ 数据库连接失败: {e}")
    sys.exit(1)
EOF
    
    # 激活Python环境并测试
    if [ -f "activate_env.sh" ]; then
        source activate_env.sh
    fi
    
    if python verify_db.py; then
        log_success "数据库连接验证通过"
        rm -f verify_db.py
    else
        log_error "数据库连接验证失败"
        rm -f verify_db.py
        return 1
    fi
}

# 验证服务状态
verify_service_status() {
    log_info "验证服务状态..."
    
    cd "$PROJECT_DIR"
    
    if [ -f "manage_server.sh" ]; then
        # 检查服务状态
        if ./manage_server.sh status | grep -q "运行中"; then
            log_success "算法智能体服务正在运行"
        else
            log_error "算法智能体服务未运行"
            return 1
        fi
        
        # 检查端口监听
        if netstat -tlnp | grep -q ":8008"; then
            log_success "端口8008正在监听"
        else
            log_error "端口8008未监听"
            return 1
        fi
    else
        log_error "管理脚本不存在"
        return 1
    fi
}

# 验证HTTP接口
verify_http_endpoints() {
    log_info "验证HTTP接口..."
    
    # 健康检查
    if curl -s -f http://localhost:8008/health >/dev/null; then
        log_success "HTTP健康检查通过"
    else
        log_error "HTTP健康检查失败"
        return 1
    fi
    
    # API文档
    if curl -s -f http://localhost:8008/docs >/dev/null; then
        log_success "API文档可访问"
    else
        log_warning "API文档访问失败"
    fi
}

# 验证WebSocket连接
verify_websocket() {
    log_info "验证WebSocket连接..."
    
    cd "$PROJECT_DIR"
    
    if [ -f "manage_server.sh" ]; then
        # 执行WebSocket测试
        log_info "执行WebSocket连接测试..."
        
        # 捕获测试输出
        test_output=$(./manage_server.sh test 2>&1)
        
        if echo "$test_output" | grep -q "连接成功\|测试成功\|✅"; then
            log_success "WebSocket连接测试通过"
        else
            log_warning "WebSocket连接测试可能有问题"
            echo "测试输出:"
            echo "$test_output"
        fi
    else
        log_error "管理脚本不存在，无法测试WebSocket"
        return 1
    fi
}

# 验证日志
verify_logs() {
    log_info "检查服务日志..."
    
    cd "$PROJECT_DIR"
    
    if [ -f "manage_server.sh" ]; then
        # 获取最近的日志
        recent_logs=$(./manage_server.sh logs | tail -20)
        
        # 检查是否有错误
        if echo "$recent_logs" | grep -i "error\|exception\|failed" | grep -v "❌\|✅"; then
            log_warning "发现错误日志，请检查:"
            echo "$recent_logs" | grep -i "error\|exception\|failed" | head -5
        else
            log_success "日志检查正常"
        fi
        
        # 检查数据库连接日志
        if echo "$recent_logs" | grep -q "$NEW_HOST"; then
            log_success "日志显示正在使用新数据库地址"
        else
            log_warning "日志中未发现新数据库地址"
        fi
    fi
}

# 性能测试
performance_test() {
    log_info "执行简单性能测试..."
    
    # 测试HTTP响应时间
    start_time=$(date +%s%N)
    curl -s http://localhost:8008/health >/dev/null
    end_time=$(date +%s%N)
    
    response_time=$(( (end_time - start_time) / 1000000 ))
    
    if [ $response_time -lt 1000 ]; then
        log_success "HTTP响应时间: ${response_time}ms (良好)"
    elif [ $response_time -lt 3000 ]; then
        log_warning "HTTP响应时间: ${response_time}ms (一般)"
    else
        log_error "HTTP响应时间: ${response_time}ms (较慢)"
    fi
}

# 生成验证报告
generate_report() {
    log_info "生成验证报告..."
    
    report_file="migration_verification_report_$(date +%Y%m%d_%H%M%S).txt"
    
    cat > "$report_file" << EOF
MySQL数据库迁移验证报告
========================

迁移时间: $(date)
目标服务器: $NEW_HOST:$MYSQL_PORT
数据库: $MYSQL_DB

验证结果:
EOF
    
    # 添加验证结果到报告
    echo "✅ 配置文件验证: 通过" >> "$report_file"
    echo "✅ 数据库连接验证: 通过" >> "$report_file"
    echo "✅ 服务状态验证: 通过" >> "$report_file"
    echo "✅ HTTP接口验证: 通过" >> "$report_file"
    echo "✅ WebSocket验证: 通过" >> "$report_file"
    echo "✅ 日志检查: 通过" >> "$report_file"
    
    log_success "验证报告已生成: $report_file"
}

# 主函数
main() {
    echo "🔍 MySQL数据库迁移验证脚本"
    echo "验证从Docker容器到独立服务器($NEW_HOST)的迁移结果"
    echo "=" * 60
    
    # 执行所有验证步骤
    if verify_config && \
       verify_database_connection && \
       verify_service_status && \
       verify_http_endpoints && \
       verify_websocket && \
       verify_logs; then
        
        # 执行性能测试
        performance_test
        
        # 生成报告
        generate_report
        
        log_success "🎉 所有验证通过！迁移成功！"
        echo ""
        echo "迁移验证摘要:"
        echo "  ✅ 数据库连接: $NEW_HOST:$MYSQL_PORT"
        echo "  ✅ 服务状态: 正常运行"
        echo "  ✅ 功能验证: 全部通过"
        echo ""
        echo "建议:"
        echo "  1. 继续监控服务运行24小时"
        echo "  2. 测试完整业务流程"
        echo "  3. 确认无误后可清理Docker容器"
        
    else
        log_error "验证过程中发现问题，请检查并修复"
        exit 1
    fi
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
