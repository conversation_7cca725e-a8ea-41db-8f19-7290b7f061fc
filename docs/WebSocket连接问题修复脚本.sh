#!/bin/bash
# WebSocket连接问题修复脚本
# 修复 "WebSocket is not connected. Need to call 'accept' first" 错误

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
PROJECT_DIR="/data/agent"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 检查项目目录
check_project_dir() {
    if [ ! -d "$PROJECT_DIR" ]; then
        log_error "项目目录不存在: $PROJECT_DIR"
        exit 1
    fi
    
    if [ ! -f "$PROJECT_DIR/agents/predict_algorith_agent/api/websocket_routes.py" ]; then
        log_error "WebSocket路由文件不存在"
        exit 1
    fi
    
    log_success "项目目录检查通过"
}

# 创建备份
create_backup() {
    log_info "创建文件备份..."
    
    cd "$PROJECT_DIR"
    
    BACKUP_DIR="backup/websocket_fix_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # 备份需要修改的文件
    cp agents/predict_algorith_agent/api/websocket_routes.py "$BACKUP_DIR/"
    cp agents/predict_algorith_agent/network/websocket_manager.py "$BACKUP_DIR/"
    
    log_success "备份完成: $BACKUP_DIR"
    echo "$BACKUP_DIR" > .websocket_fix_backup
}

# 停止服务
stop_service() {
    log_info "停止算法智能体服务..."
    
    cd "$PROJECT_DIR"
    
    if [ -f "manage_server.sh" ]; then
        ./manage_server.sh stop
        sleep 3
        log_success "服务已停止"
    else
        log_warning "管理脚本不存在，手动检查进程..."
        pkill -f "predict_main.py" || true
    fi
}

# 修复WebSocket路由文件
fix_websocket_routes() {
    log_info "修复WebSocket路由文件..."
    
    cd "$PROJECT_DIR"
    
    # 创建修复后的文件内容
    cat > agents/predict_algorith_agent/api/websocket_routes_fixed.py << 'EOF'
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
WebSocket路由模块 - 定义WebSocket端点和处理逻辑
"""

import json
import uuid
import time
import logging
from datetime import datetime
from typing import Optional
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Query, HTTPException
from fastapi.responses import JSONResponse

# 导入WebSocket连接管理器
from agents.predict_algorith_agent.network.websocket_manager import ConnectionManager

# 配置日志
logger = logging.getLogger(__name__)

# 创建WebSocket路由器
router = APIRouter()

# 创建连接管理器实例
connection_manager = ConnectionManager()

@router.websocket("/ws/chat")
async def websocket_chat_endpoint(
    websocket: WebSocket,
    session_id: Optional[str] = Query(None, description="会话ID，如果不提供将自动生成"),
    user_id: Optional[str] = Query(None, description="用户ID，从web系统传入"),
    username: Optional[str] = Query(None, description="用户名，从web系统传入")
):
    """
    WebSocket聊天端点

    连接URL示例：
    ws://localhost:8008/api/ws/chat?session_id=user123&user_id=12345&username=张三

    消息格式：
    {
        "type": "chat",
        "data": {
            "message": "我想用LSTM做预测"
        }
    }
    """
    # 验证用户信息
    if not user_id:
        logger.warning(f"WebSocket连接缺少用户ID: session_id={session_id}")
        await websocket.close(code=4001, reason="Missing user_id")
        return

    # 如果没有提供session_id，自动生成一个32位UUID
    if not session_id:
        session_id = uuid.uuid4().hex

    logger.info(f"WebSocket连接请求: session_id={session_id}, user_id={user_id}, username={username}")

    try:
        # 建立连接，传递用户信息
        await connection_manager.connect(websocket, session_id, user_info={
            "user_id": user_id,
            "username": username or f"user_{user_id}"
        })
        
        # 消息循环
        while True:
            try:
                # 接收消息
                data = await websocket.receive_text()
                message_data = json.loads(data)
                
                # 处理消息
                await connection_manager.handle_message(websocket, session_id, message_data)
                
            except WebSocketDisconnect:
                logger.info(f"WebSocket正常断开: session_id={session_id}")
                break
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析错误: session_id={session_id}, error={e}")
                # 直接发送错误响应，不通过connection_manager
                try:
                    error_response = {
                        "type": "error",
                        "data": {"error": "消息格式错误，请发送有效的JSON"},
                        "session_id": session_id,
                        "status": "error"
                    }
                    await websocket.send_text(json.dumps(error_response, ensure_ascii=False))
                except Exception as send_error:
                    logger.error(f"发送JSON错误响应失败: session_id={session_id}, error={send_error}")
                    break
            except Exception as e:
                logger.error(f"消息处理异常: session_id={session_id}, error={e}")
                # 检查连接是否仍然有效
                if session_id in connection_manager.active_connections:
                    try:
                        # 直接发送错误响应，避免状态问题
                        error_response = {
                            "type": "error",
                            "data": {"error": f"消息处理失败: {str(e)}"},
                            "session_id": session_id,
                            "status": "error"
                        }
                        await websocket.send_text(json.dumps(error_response, ensure_ascii=False))
                    except Exception as send_error:
                        logger.error(f"发送错误响应失败: session_id={session_id}, error={send_error}")
                        # 连接可能已断开，清理连接
                        connection_manager.disconnect(session_id)
                        break
                
    except Exception as e:
        logger.error(f"WebSocket连接异常: session_id={session_id}, error={e}")
    finally:
        # 清理连接
        connection_manager.disconnect(session_id)
        logger.info(f"WebSocket连接清理完成: session_id={session_id}")

@router.get("/ws/health")
async def websocket_health():
    """WebSocket健康检查"""
    stats = connection_manager.get_connection_stats()
    return JSONResponse({
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "connections": stats
    })

@router.get("/ws/stats")
async def websocket_stats():
    """获取WebSocket连接统计"""
    return JSONResponse(connection_manager.get_connection_stats())

@router.post("/ws/broadcast")
async def broadcast_message(message: dict):
    """广播消息到所有连接"""
    try:
        await connection_manager.broadcast(message)
        return {"status": "success", "message": "消息已广播"}
    except Exception as e:
        logger.error(f"广播消息失败: {e}")
        raise HTTPException(status_code=500, detail=f"广播失败: {str(e)}")

@router.delete("/ws/disconnect/{session_id}")
async def force_disconnect(session_id: str):
    """强制断开指定会话"""
    try:
        if session_id in connection_manager.active_connections:
            connection_manager.disconnect(session_id)
            return {"status": "success", "message": f"会话 {session_id} 已断开"}
        else:
            return {"status": "not_found", "message": f"会话 {session_id} 不存在"}
    except Exception as e:
        logger.error(f"强制断开会话失败: {e}")
        raise HTTPException(status_code=500, detail=f"断开失败: {str(e)}")
EOF

    # 替换原文件
    mv agents/predict_algorith_agent/api/websocket_routes.py agents/predict_algorith_agent/api/websocket_routes.py.old
    mv agents/predict_algorith_agent/api/websocket_routes_fixed.py agents/predict_algorith_agent/api/websocket_routes.py
    
    log_success "WebSocket路由文件已修复"
}

# 启动服务
start_service() {
    log_info "启动算法智能体服务..."
    
    cd "$PROJECT_DIR"
    
    if [ -f "manage_server.sh" ]; then
        ./manage_server.sh start
        sleep 10
        
        # 检查服务状态
        if ./manage_server.sh status | grep -q "运行中"; then
            log_success "服务启动成功"
        else
            log_error "服务启动失败"
            return 1
        fi
    else
        log_error "管理脚本不存在"
        return 1
    fi
}

# 测试修复效果
test_fix() {
    log_info "测试WebSocket连接修复效果..."
    
    cd "$PROJECT_DIR"
    
    # 等待服务完全启动
    sleep 5
    
    # 测试HTTP健康检查
    if curl -s http://localhost:8008/health | grep -q "ok"; then
        log_success "HTTP健康检查通过"
    else
        log_warning "HTTP健康检查失败"
    fi
    
    # 测试WebSocket连接
    if [ -f "manage_server.sh" ]; then
        log_info "执行WebSocket连接测试..."
        if ./manage_server.sh test 2>&1 | grep -q "连接成功\|测试成功\|✅"; then
            log_success "WebSocket连接测试通过"
        else
            log_warning "WebSocket连接测试可能有问题，请查看详细日志"
        fi
    fi
}

# 回滚函数
rollback() {
    log_warning "执行回滚操作..."
    
    cd "$PROJECT_DIR"
    
    # 停止服务
    if [ -f "manage_server.sh" ]; then
        ./manage_server.sh stop
    fi
    
    # 恢复文件
    if [ -f ".websocket_fix_backup" ]; then
        BACKUP_DIR=$(cat .websocket_fix_backup)
        if [ -f "$BACKUP_DIR/websocket_routes.py" ]; then
            cp "$BACKUP_DIR/websocket_routes.py" agents/predict_algorith_agent/api/websocket_routes.py
            log_success "WebSocket路由文件已回滚"
        fi
        
        if [ -f "$BACKUP_DIR/websocket_manager.py" ]; then
            cp "$BACKUP_DIR/websocket_manager.py" agents/predict_algorith_agent/network/websocket_manager.py
            log_success "WebSocket管理器文件已回滚"
        fi
    fi
    
    # 重启服务
    if [ -f "manage_server.sh" ]; then
        ./manage_server.sh start
    fi
    
    log_success "回滚完成"
}

# 主函数
main() {
    echo "🔧 WebSocket连接问题修复脚本"
    echo "修复 'WebSocket is not connected. Need to call accept first' 错误"
    echo "=" * 60
    
    # 确认执行
    read -p "确认执行修复？(y/N): " confirm
    if [[ ! $confirm =~ ^[Yy]$ ]]; then
        log_info "修复已取消"
        exit 0
    fi
    
    # 执行修复步骤
    if check_project_dir && \
       create_backup && \
       stop_service && \
       fix_websocket_routes && \
       start_service && \
       test_fix; then
        
        log_success "🎉 WebSocket连接问题修复完成！"
        echo ""
        echo "修复摘要:"
        echo "  ✅ 修复了WebSocket状态管理问题"
        echo "  ✅ 改进了错误处理机制"
        echo "  ✅ 增强了连接状态检查"
        echo ""
        echo "建议:"
        echo "  1. 测试聊天功能是否正常"
        echo "  2. 监控服务日志"
        echo "  3. 如有问题可执行回滚"
        
    else
        log_error "修复过程中出现错误"
        read -p "是否执行回滚？(y/N): " rollback_confirm
        if [[ $rollback_confirm =~ ^[Yy]$ ]]; then
            rollback
        fi
        exit 1
    fi
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
