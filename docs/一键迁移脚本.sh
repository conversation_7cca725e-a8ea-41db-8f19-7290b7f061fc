#!/bin/bash
# MySQL数据库迁移一键脚本
# 从*********** Docker容器迁移到***********独立服务器

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
PROJECT_DIR="/data/agent"
OLD_HOST="***********"
NEW_HOST="***********"
MYSQL_USER="root"
MYSQL_PASSWORD="Spsm2021+"
MYSQL_DB="indusaio_agent"
MYSQL_PORT="3306"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 检查项目目录
check_project_dir() {
    if [ ! -d "$PROJECT_DIR" ]; then
        log_error "项目目录不存在: $PROJECT_DIR"
        exit 1
    fi
    
    if [ ! -f "$PROJECT_DIR/agents/config.py" ]; then
        log_error "配置文件不存在: $PROJECT_DIR/agents/config.py"
        exit 1
    fi
    
    log_success "项目目录检查通过"
}

# 创建备份
create_backup() {
    log_info "创建配置文件备份..."
    
    cd "$PROJECT_DIR"
    
    BACKUP_DIR="backup/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # 备份关键配置文件
    cp agents/config.py "$BACKUP_DIR/config.py.bak"
    
    if [ -f ".env" ]; then
        cp .env "$BACKUP_DIR/.env.bak"
    fi
    
    log_success "备份完成: $BACKUP_DIR"
    echo "$BACKUP_DIR" > .last_backup
}

# 停止服务
stop_service() {
    log_info "停止算法智能体服务..."
    
    cd "$PROJECT_DIR"
    
    if [ -f "manage_server.sh" ]; then
        ./manage_server.sh stop
        sleep 3
        log_success "服务已停止"
    else
        log_warning "管理脚本不存在，手动检查进程..."
        pkill -f "predict_main.py" || true
    fi
}

# 修改数据库配置
update_database_config() {
    log_info "更新数据库连接配置..."
    
    cd "$PROJECT_DIR"
    
    # 备份原配置文件
    cp agents/config.py agents/config.py.tmp
    
    # 使用sed替换数据库主机配置
    sed -i "s/MYSQL_HOST = os.getenv(\"DB_HOST\", \"$OLD_HOST\")/MYSQL_HOST = os.getenv(\"DB_HOST\", \"$NEW_HOST\")/g" agents/config.py
    
    # 如果上面的替换没有成功，尝试其他可能的格式
    sed -i "s/\"$OLD_HOST\"/\"$NEW_HOST\"/g" agents/config.py
    sed -i "s/'$OLD_HOST'/'$NEW_HOST'/g" agents/config.py
    
    # 更新.env文件（如果存在）
    if [ -f ".env" ]; then
        sed -i "s/DB_HOST=$OLD_HOST/DB_HOST=$NEW_HOST/g" .env
        log_info "已更新.env文件"
    fi
    
    log_success "数据库配置已更新"
}

# 测试新数据库连接
test_new_connection() {
    log_info "测试新数据库连接..."
    
    cd "$PROJECT_DIR"
    
    # 创建测试脚本
    cat > test_connection.py << EOF
#!/usr/bin/env python3
import sys
sys.path.insert(0, '$PROJECT_DIR')

try:
    import pymysql
    from agents.config import MYSQL_HOST, MYSQL_PORT, MYSQL_USER, MYSQL_PASSWORD, MYSQL_DB
    
    print(f"连接目标: {MYSQL_HOST}:{MYSQL_PORT}")
    print(f"数据库: {MYSQL_DB}")
    
    conn = pymysql.connect(
        host=MYSQL_HOST,
        port=MYSQL_PORT,
        user=MYSQL_USER,
        password=MYSQL_PASSWORD,
        database=MYSQL_DB,
        charset='utf8mb4',
        ssl_disabled=True,
        connect_timeout=10
    )
    
    cursor = conn.cursor()
    cursor.execute("SELECT VERSION()")
    version = cursor.fetchone()
    print(f"MySQL版本: {version[0]}")
    
    cursor.execute("SHOW TABLES")
    tables = cursor.fetchall()
    print(f"表数量: {len(tables)}")
    
    cursor.close()
    conn.close()
    
    print("✅ 数据库连接测试成功")
    
except Exception as e:
    print(f"❌ 数据库连接失败: {e}")
    sys.exit(1)
EOF
    
    # 激活Python环境并测试
    if [ -f "activate_env.sh" ]; then
        source activate_env.sh
    fi
    
    if python test_connection.py; then
        log_success "新数据库连接测试成功"
        rm -f test_connection.py
    else
        log_error "新数据库连接测试失败"
        rm -f test_connection.py
        return 1
    fi
}

# 启动服务
start_service() {
    log_info "启动算法智能体服务..."
    
    cd "$PROJECT_DIR"
    
    if [ -f "manage_server.sh" ]; then
        ./manage_server.sh start
        sleep 10
        
        # 检查服务状态
        if ./manage_server.sh status | grep -q "运行中"; then
            log_success "服务启动成功"
        else
            log_error "服务启动失败"
            return 1
        fi
    else
        log_error "管理脚本不存在"
        return 1
    fi
}

# 验证服务功能
verify_service() {
    log_info "验证服务功能..."
    
    cd "$PROJECT_DIR"
    
    # 测试HTTP健康检查
    if curl -s http://localhost:8008/health | grep -q "ok"; then
        log_success "HTTP健康检查通过"
    else
        log_warning "HTTP健康检查失败"
    fi
    
    # 测试WebSocket连接
    if [ -f "manage_server.sh" ]; then
        log_info "执行WebSocket连接测试..."
        if ./manage_server.sh test 2>&1 | grep -q "连接成功\|测试成功"; then
            log_success "WebSocket连接测试通过"
        else
            log_warning "WebSocket连接测试可能有问题，请查看详细日志"
        fi
    fi
}

# 回滚函数
rollback() {
    log_warning "执行回滚操作..."
    
    cd "$PROJECT_DIR"
    
    # 停止服务
    if [ -f "manage_server.sh" ]; then
        ./manage_server.sh stop
    fi
    
    # 恢复配置文件
    if [ -f ".last_backup" ]; then
        BACKUP_DIR=$(cat .last_backup)
        if [ -f "$BACKUP_DIR/config.py.bak" ]; then
            cp "$BACKUP_DIR/config.py.bak" agents/config.py
            log_success "配置文件已回滚"
        fi
        
        if [ -f "$BACKUP_DIR/.env.bak" ]; then
            cp "$BACKUP_DIR/.env.bak" .env
            log_success ".env文件已回滚"
        fi
    fi
    
    # 重启服务
    if [ -f "manage_server.sh" ]; then
        ./manage_server.sh start
    fi
    
    log_success "回滚完成"
}

# 主函数
main() {
    echo "🚀 MySQL数据库迁移脚本"
    echo "从 $OLD_HOST (Docker容器) 迁移到 $NEW_HOST (独立服务器)"
    echo "=" * 60
    
    # 确认执行
    read -p "确认执行迁移？(y/N): " confirm
    if [[ ! $confirm =~ ^[Yy]$ ]]; then
        log_info "迁移已取消"
        exit 0
    fi
    
    # 执行迁移步骤
    if check_project_dir && \
       create_backup && \
       stop_service && \
       update_database_config && \
       test_new_connection && \
       start_service && \
       verify_service; then
        
        log_success "🎉 数据库迁移完成！"
        echo ""
        echo "迁移摘要:"
        echo "  ✅ 从 $OLD_HOST 迁移到 $NEW_HOST"
        echo "  ✅ 服务正常运行"
        echo "  ✅ 数据库连接正常"
        echo ""
        echo "后续建议:"
        echo "  1. 监控服务运行状态"
        echo "  2. 测试完整业务功能"
        echo "  3. 确认无误后可删除备份文件"
        
    else
        log_error "迁移过程中出现错误"
        read -p "是否执行回滚？(y/N): " rollback_confirm
        if [[ $rollback_confirm =~ ^[Yy]$ ]]; then
            rollback
        fi
        exit 1
    fi
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
