#!/bin/bash
# 快速修复_gateway主机MySQL权限问题
# 专门针对 "Access denied for user 'root'@'_gateway'" 错误

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
MYSQL_PASSWORD="Spsm2021+"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

echo "🔧 快速修复 MySQL '_gateway' 权限问题"
echo "=" * 50

# 检查当前主机信息
hostname=$(hostname)
local_ip=$(hostname -I | awk '{print $1}')

log_info "当前主机名: $hostname"
log_info "当前IP地址: $local_ip"

# 创建SQL修复脚本
log_info "创建MySQL权限修复脚本..."

cat > /tmp/fix_gateway_permissions.sql << EOF
-- 显示当前连接信息
SELECT USER(), @@hostname, CONNECTION_ID();

-- 为 _gateway 主机创建用户权限（关键修复）
CREATE USER IF NOT EXISTS 'root'@'_gateway' IDENTIFIED BY '$MYSQL_PASSWORD';
GRANT ALL PRIVILEGES ON *.* TO 'root'@'_gateway' WITH GRANT OPTION;

-- 为 gateway 主机创建用户权限（备用）
CREATE USER IF NOT EXISTS 'root'@'gateway' IDENTIFIED BY '$MYSQL_PASSWORD';
GRANT ALL PRIVILEGES ON *.* TO 'root'@'gateway' WITH GRANT OPTION;

-- 为当前主机名创建权限
CREATE USER IF NOT EXISTS 'root'@'$hostname' IDENTIFIED BY '$MYSQL_PASSWORD';
GRANT ALL PRIVILEGES ON *.* TO 'root'@'$hostname' WITH GRANT OPTION;

-- 为当前IP创建权限
CREATE USER IF NOT EXISTS 'root'@'$local_ip' IDENTIFIED BY '$MYSQL_PASSWORD';
GRANT ALL PRIVILEGES ON *.* TO 'root'@'$local_ip' WITH GRANT OPTION;

-- 确保localhost权限存在
CREATE USER IF NOT EXISTS 'root'@'localhost' IDENTIFIED BY '$MYSQL_PASSWORD';
GRANT ALL PRIVILEGES ON *.* TO 'root'@'localhost' WITH GRANT OPTION;

-- 刷新权限
FLUSH PRIVILEGES;

-- 显示所有root用户
SELECT User, Host FROM mysql.user WHERE User = 'root' ORDER BY Host;

-- 测试连接
SELECT 'MySQL权限修复完成' AS Status;
EOF

# 执行SQL修复
log_info "执行MySQL权限修复..."

if mysql -h localhost -u root -p"$MYSQL_PASSWORD" < /tmp/fix_gateway_permissions.sql; then
    log_success "✅ MySQL权限修复成功！"
else
    log_error "❌ MySQL权限修复失败"
    log_info "尝试其他连接方式..."
    
    # 尝试不指定主机
    if mysql -u root -p"$MYSQL_PASSWORD" < /tmp/fix_gateway_permissions.sql; then
        log_success "✅ MySQL权限修复成功（使用默认连接）！"
    else
        log_error "❌ 所有MySQL连接方式都失败"
        log_info "请手动执行以下命令："
        log_info "mysql -u root -p"
        log_info "然后执行 /tmp/fix_gateway_permissions.sql 中的SQL语句"
        exit 1
    fi
fi

# 清理临时文件
rm -f /tmp/fix_gateway_permissions.sql

# 测试修复结果
log_info "测试修复结果..."

cat > /tmp/test_mysql_connection.py << 'EOF'
#!/usr/bin/env python3
import sys
sys.path.insert(0, '/data/agent')

try:
    from agents.config import MYSQL_HOST, MYSQL_PORT, MYSQL_USER, MYSQL_PASSWORD, MYSQL_DB
    import pymysql
    
    print("🔍 测试MySQL连接...")
    
    # 测试连接
    conn = pymysql.connect(
        host=MYSQL_HOST,
        port=MYSQL_PORT,
        user=MYSQL_USER,
        password=MYSQL_PASSWORD,
        database=MYSQL_DB,
        charset='utf8mb4',
        ssl_disabled=True,
        connect_timeout=10
    )
    
    cursor = conn.cursor()
    cursor.execute("SELECT USER(), @@hostname")
    user_info = cursor.fetchone()
    print(f"✅ MySQL连接成功！")
    print(f"   当前用户: {user_info[0]}")
    print(f"   服务器主机名: {user_info[1]}")
    
    # 测试数据库操作
    cursor.execute("SHOW TABLES")
    tables = cursor.fetchall()
    print(f"   数据库 {MYSQL_DB} 中有 {len(tables)} 个表")
    
    cursor.close()
    conn.close()
    
    print("🎉 数据库连接测试通过！")
    
except Exception as e:
    print(f"❌ MySQL连接仍然失败: {e}")
    sys.exit(1)
EOF

# 激活Python环境并测试
cd /data/agent

if [ -f "activate_env.sh" ]; then
    source activate_env.sh
    log_info "已激活Python环境"
fi

python /tmp/test_mysql_connection.py

# 清理测试文件
rm -f /tmp/test_mysql_connection.py

# 重启服务
log_info "重启算法智能体服务..."

if [ -f "manage_server.sh" ]; then
    log_info "停止现有服务..."
    ./manage_server.sh stop
    
    sleep 3
    
    log_info "启动服务..."
    ./manage_server.sh start
    
    sleep 5
    
    log_info "检查服务状态..."
    ./manage_server.sh status
    
else
    log_error "管理脚本不存在"
fi

log_success "🎉 快速修复完成！"
log_info ""
log_info "📋 接下来可以执行："
log_info "   ./manage_server.sh test    # 测试WebSocket连接"
log_info "   ./manage_server.sh logs    # 查看服务日志"
log_info "   ./manage_server.sh status  # 查看服务状态"
