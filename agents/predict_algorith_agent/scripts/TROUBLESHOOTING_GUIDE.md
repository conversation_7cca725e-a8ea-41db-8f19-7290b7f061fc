# 算法智能体部署问题排查指南

## 🚨 当前问题分析

根据错误日志，主要问题是：

1. **MySQL连接失败**: `Access denied for user 'root'@'_gateway'` (注意是下划线+gateway)
2. **用户权限问题**: MySQL认为连接来自'_gateway'主机，但数据库中没有此权限
3. **WebSocket服务启动失败**: 由于数据库连接问题导致
4. **WebSocket连接错误**: `WebSocket is not connected. Need to call 'accept' first`

## 🔧 快速修复步骤

### 🚀 一键修复（推荐）

```bash
cd /data/agent
chmod +x agents/predict_algorith_agent/scripts/quick_fix_gateway.sh

# 执行一键修复
./agents/predict_algorith_agent/scripts/quick_fix_gateway.sh
```

### 步骤1: 运行诊断脚本

```bash
cd /data/agent
chmod +x agents/predict_algorith_agent/scripts/diagnose_connection.py
chmod +x agents/predict_algorith_agent/scripts/fix_connection_issues.sh

# 运行诊断
./agents/predict_algorith_agent/scripts/fix_connection_issues.sh diagnose
```

### 步骤2: 检查服务状态

```bash
# 检查MySQL和Redis服务
./agents/predict_algorith_agent/scripts/fix_connection_issues.sh check
```

### 步骤3: 修复MySQL权限问题

```bash
# 自动修复MySQL用户权限
./agents/predict_algorith_agent/scripts/fix_connection_issues.sh fix-mysql
```

### 步骤4: 测试连接

```bash
# 测试数据库连接
./agents/predict_algorith_agent/scripts/fix_connection_issues.sh test
```

### 步骤5: 重启服务

```bash
# 重启算法智能体服务
./agents/predict_algorith_agent/scripts/fix_connection_issues.sh restart
```

## 🔍 手动排查步骤

### 1. 检查主机名解析

```bash
# 查看当前主机名
hostname

# 查看IP地址
hostname -I

# 如果主机名是'gateway'，这就是问题根源
```

### 2. 手动修复MySQL权限

如果自动修复失败，手动执行：

```bash
# 连接到MySQL
mysql -h localhost -u root -p

# 执行以下SQL命令
```

```sql
-- 获取当前主机信息
SELECT @@hostname, USER();

-- 为gateway主机创建权限
CREATE USER IF NOT EXISTS 'root'@'gateway' IDENTIFIED BY 'Spsm2021+';
GRANT ALL PRIVILEGES ON *.* TO 'root'@'gateway' WITH GRANT OPTION;

-- 为当前IP创建权限
CREATE USER IF NOT EXISTS 'root'@'***********' IDENTIFIED BY 'Spsm2021+';
GRANT ALL PRIVILEGES ON *.* TO 'root'@'***********' WITH GRANT OPTION;

-- 刷新权限
FLUSH PRIVILEGES;

-- 查看所有root用户
SELECT User, Host FROM mysql.user WHERE User = 'root';
```

### 3. 检查网络连接

```bash
# 测试MySQL端口
telnet *********** 3306

# 测试Redis端口
telnet *********** 6379

# 检查防火墙
systemctl status firewalld
```

### 4. 检查服务状态

```bash
# 检查MySQL服务
systemctl status mysql
# 或者
systemctl status mysqld

# 检查Redis服务
systemctl status redis
# 或者
systemctl status redis-server

# 检查端口监听
netstat -tlnp | grep -E ":(3306|6379) "
```

## 🛠️ 常见问题解决方案

### 问题1: MySQL访问被拒绝

**症状**: `Access denied for user 'root'@'_gateway'`

**解决方案**:
1. 为'_gateway'主机创建MySQL用户权限（关键）
2. 同时为'gateway'主机创建权限（备用）
3. 或者修改MySQL配置使用IP地址而不是主机名

**立即修复命令**:
```sql
CREATE USER IF NOT EXISTS 'root'@'_gateway' IDENTIFIED BY 'Spsm2021+';
GRANT ALL PRIVILEGES ON *.* TO 'root'@'_gateway' WITH GRANT OPTION;
FLUSH PRIVILEGES;
```

### 问题2: 连接超时

**症状**: `Connection timed out`

**解决方案**:
1. 检查防火墙设置
2. 确认MySQL/Redis服务正在运行
3. 检查网络连接

### 问题3: 数据库不存在

**症状**: `Unknown database 'indusaio_agent'`

**解决方案**:
```sql
-- 创建数据库
CREATE DATABASE IF NOT EXISTS indusaio_agent CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 问题4: Redis认证失败

**症状**: `NOAUTH Authentication required`

**解决方案**:
1. 检查Redis密码配置
2. 确认Redis配置文件中的密码设置

## 📋 完整修复流程

如果遇到复杂问题，执行完整修复：

```bash
# 执行完整修复流程
./agents/predict_algorith_agent/scripts/fix_connection_issues.sh fix-all
```

这将自动执行：
1. 环境检查
2. 服务状态检查
3. MySQL权限修复
4. 连接测试
5. 服务重启

## 🔄 验证修复结果

修复完成后，验证服务是否正常：

```bash
# 查看服务状态
./manage_server.sh status

# 查看日志
./manage_server.sh logs

# 测试WebSocket连接
./manage_server.sh test
```

## 📞 获取帮助

如果问题仍然存在：

1. 查看详细日志：`tail -f /data/agent/logs/server.log`
2. 运行诊断脚本获取详细信息
3. 检查系统日志：`journalctl -u mysql -f`

## 🎯 预防措施

为避免类似问题：

1. **定期备份数据库**
2. **监控服务状态**
3. **保持配置文件同步**
4. **定期更新依赖包**

---

**注意**: 所有操作都应该在***********服务器上以root权限执行。
