#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
连接诊断脚本 - 诊断MySQL和Redis连接问题
"""

import sys
import os
import socket
import pymysql
import redis
import time
from datetime import datetime

# 添加项目路径
sys.path.insert(0, '/data/agent')

def log_info(msg):
    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] ℹ️  {msg}")

def log_success(msg):
    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] ✅ {msg}")

def log_error(msg):
    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] ❌ {msg}")

def log_warning(msg):
    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] ⚠️  {msg}")

def test_network_connectivity():
    """测试网络连通性"""
    log_info("=== 网络连通性测试 ===")
    
    # 测试MySQL端口
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex(('***********', 3306))
        sock.close()
        
        if result == 0:
            log_success("MySQL端口 ***********:3306 可达")
        else:
            log_error("MySQL端口 ***********:3306 不可达")
    except Exception as e:
        log_error(f"MySQL端口测试失败: {e}")
    
    # 测试Redis端口
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex(('***********', 6379))
        sock.close()
        
        if result == 0:
            log_success("Redis端口 ***********:6379 可达")
        else:
            log_error("Redis端口 ***********:6379 不可达")
    except Exception as e:
        log_error(f"Redis端口测试失败: {e}")

def test_hostname_resolution():
    """测试主机名解析"""
    log_info("=== 主机名解析测试 ===")
    
    try:
        # 获取本机主机名
        hostname = socket.gethostname()
        log_info(f"本机主机名: {hostname}")
        
        # 获取本机IP地址
        local_ip = socket.gethostbyname(hostname)
        log_info(f"本机IP地址: {local_ip}")
        
        # 检查是否解析为gateway
        if hostname.lower() == 'gateway' or 'gateway' in hostname.lower():
            log_warning("检测到主机名包含'gateway'，这可能导致MySQL连接问题")
            log_info("建议在MySQL中为当前IP地址创建用户权限")
        
    except Exception as e:
        log_error(f"主机名解析失败: {e}")

def test_mysql_connection():
    """测试MySQL连接"""
    log_info("=== MySQL连接测试 ===")
    
    # 从配置文件导入
    try:
        from agents.config import MYSQL_HOST, MYSQL_PORT, MYSQL_USER, MYSQL_PASSWORD, MYSQL_DB
        log_info(f"MySQL配置: {MYSQL_USER}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DB}")
    except Exception as e:
        log_error(f"无法导入MySQL配置: {e}")
        return
    
    # 测试连接配置
    configs_to_test = [
        {
            'name': '标准配置',
            'config': {
                'host': MYSQL_HOST,
                'port': MYSQL_PORT,
                'user': MYSQL_USER,
                'password': MYSQL_PASSWORD,
                'database': MYSQL_DB,
                'charset': 'utf8mb4',
                'connect_timeout': 10,
                'ssl_disabled': True
            }
        },
        {
            'name': '无数据库配置',
            'config': {
                'host': MYSQL_HOST,
                'port': MYSQL_PORT,
                'user': MYSQL_USER,
                'password': MYSQL_PASSWORD,
                'charset': 'utf8mb4',
                'connect_timeout': 10,
                'ssl_disabled': True
            }
        },
        {
            'name': 'localhost配置',
            'config': {
                'host': 'localhost',
                'port': MYSQL_PORT,
                'user': MYSQL_USER,
                'password': MYSQL_PASSWORD,
                'database': MYSQL_DB,
                'charset': 'utf8mb4',
                'connect_timeout': 10,
                'ssl_disabled': True
            }
        }
    ]
    
    for test_config in configs_to_test:
        log_info(f"测试 {test_config['name']}...")
        try:
            conn = pymysql.connect(**test_config['config'])
            log_success(f"{test_config['name']} 连接成功")
            
            # 测试查询
            cursor = conn.cursor()
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()[0]
            log_info(f"MySQL版本: {version}")
            
            # 检查用户权限
            cursor.execute("SELECT USER(), @@hostname")
            user_info = cursor.fetchone()
            log_info(f"当前用户: {user_info[0]}, 服务器主机名: {user_info[1]}")
            
            cursor.close()
            conn.close()
            
        except pymysql.err.OperationalError as e:
            error_code = e.args[0] if e.args else 0
            error_msg = e.args[1] if len(e.args) > 1 else str(e)
            log_error(f"{test_config['name']} 连接失败: [{error_code}] {error_msg}")
            
            if error_code == 1045:
                log_warning("访问被拒绝 - 可能的原因:")
                log_warning("1. 用户名或密码错误")
                log_warning("2. 用户没有从当前主机连接的权限")
                log_warning("3. 需要为当前IP地址或主机名创建用户权限")
            elif error_code == 2003:
                log_warning("无法连接到服务器 - 可能的原因:")
                log_warning("1. MySQL服务未启动")
                log_warning("2. 防火墙阻止连接")
                log_warning("3. 网络连接问题")
                
        except Exception as e:
            log_error(f"{test_config['name']} 连接异常: {e}")

def test_redis_connection():
    """测试Redis连接"""
    log_info("=== Redis连接测试 ===")
    
    try:
        from agents.config import REDIS_HOST, REDIS_PORT, REDIS_PASSWORD, REDIS_DB
        log_info(f"Redis配置: {REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}")
    except Exception as e:
        log_error(f"无法导入Redis配置: {e}")
        return
    
    try:
        # 测试Redis连接
        r = redis.Redis(
            host=REDIS_HOST,
            port=REDIS_PORT,
            password=REDIS_PASSWORD,
            db=REDIS_DB,
            decode_responses=True,
            socket_connect_timeout=10,
            socket_timeout=10
        )
        
        # 测试ping
        r.ping()
        log_success("Redis连接成功")
        
        # 测试基本操作
        test_key = f"test_connection_{int(time.time())}"
        r.set(test_key, "test_value", ex=60)
        value = r.get(test_key)
        
        if value == "test_value":
            log_success("Redis读写测试成功")
            r.delete(test_key)
        else:
            log_warning("Redis读写测试失败")
            
    except redis.ConnectionError as e:
        log_error(f"Redis连接失败: {e}")
        log_warning("可能的原因:")
        log_warning("1. Redis服务未启动")
        log_warning("2. 密码错误")
        log_warning("3. 防火墙阻止连接")
    except Exception as e:
        log_error(f"Redis测试异常: {e}")

def check_mysql_user_permissions():
    """检查MySQL用户权限"""
    log_info("=== MySQL用户权限检查 ===")
    
    try:
        from agents.config import MYSQL_HOST, MYSQL_PORT, MYSQL_USER, MYSQL_PASSWORD
        
        # 尝试连接并查询用户权限
        conn = pymysql.connect(
            host=MYSQL_HOST,
            port=MYSQL_PORT,
            user=MYSQL_USER,
            password=MYSQL_PASSWORD,
            charset='utf8mb4',
            connect_timeout=10,
            ssl_disabled=True
        )
        
        cursor = conn.cursor()
        
        # 查询当前用户权限
        cursor.execute("SHOW GRANTS FOR CURRENT_USER()")
        grants = cursor.fetchall()
        
        log_info("当前用户权限:")
        for grant in grants:
            log_info(f"  {grant[0]}")
        
        # 查询所有root用户
        cursor.execute("SELECT User, Host FROM mysql.user WHERE User = 'root'")
        root_users = cursor.fetchall()
        
        log_info("数据库中的root用户:")
        for user in root_users:
            log_info(f"  root@{user[1]}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        log_error(f"权限检查失败: {e}")

def generate_mysql_fix_commands():
    """生成MySQL修复命令"""
    log_info("=== MySQL修复建议 ===")
    
    try:
        hostname = socket.gethostname()
        local_ip = socket.gethostbyname(hostname)
        
        log_info("如果连接问题是由于用户权限导致，可以尝试以下SQL命令:")
        log_info("")
        log_info("-- 为当前主机名创建用户权限")
        log_info(f"CREATE USER 'root'@'{hostname}' IDENTIFIED BY 'Spsm2021+';")
        log_info(f"GRANT ALL PRIVILEGES ON *.* TO 'root'@'{hostname}' WITH GRANT OPTION;")
        log_info("")
        log_info("-- 为当前IP地址创建用户权限")
        log_info(f"CREATE USER 'root'@'{local_ip}' IDENTIFIED BY 'Spsm2021+';")
        log_info(f"GRANT ALL PRIVILEGES ON *.* TO 'root'@'{local_ip}' WITH GRANT OPTION;")
        log_info("")
        log_info("-- 刷新权限")
        log_info("FLUSH PRIVILEGES;")
        log_info("")
        log_info("注意: 请在MySQL服务器上以管理员身份执行这些命令")
        
    except Exception as e:
        log_error(f"生成修复命令失败: {e}")

def main():
    """主函数"""
    print("🔍 算法智能体连接诊断工具")
    print("=" * 50)
    
    # 网络连通性测试
    test_network_connectivity()
    print()
    
    # 主机名解析测试
    test_hostname_resolution()
    print()
    
    # MySQL连接测试
    test_mysql_connection()
    print()
    
    # Redis连接测试
    test_redis_connection()
    print()
    
    # MySQL用户权限检查
    check_mysql_user_permissions()
    print()
    
    # 生成修复建议
    generate_mysql_fix_commands()
    print()
    
    log_info("诊断完成！请根据上述结果进行相应的修复操作。")

if __name__ == "__main__":
    main()
