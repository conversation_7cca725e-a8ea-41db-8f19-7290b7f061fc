#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WebSocket聊天功能调试测试脚本
专门用于调试聊天消息处理问题
"""

import asyncio
import websockets
import json
import time
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def debug_chat_test():
    """调试聊天功能"""
    
    # 连接参数
    host = "***********"
    port = 8008
    session_id = f"debug_session_{int(time.time())}"
    user_id = "debug_user"
    username = "调试用户"
    
    url = f"ws://{host}:{port}/api/ws/chat?session_id={session_id}&user_id={user_id}&username={username}"
    
    logger.info(f"🔗 连接URL: {url}")
    
    try:
        # 建立连接
        websocket = await asyncio.wait_for(
            websockets.connect(url),
            timeout=10.0
        )
        logger.info("✅ WebSocket连接成功")
        
        # 1. 等待欢迎消息
        logger.info("⏳ 等待欢迎消息...")
        try:
            welcome_text = await asyncio.wait_for(websocket.recv(), timeout=10.0)
            welcome = json.loads(welcome_text)
            logger.info(f"📥 欢迎消息类型: {welcome.get('type')}")
            logger.info(f"📋 欢迎消息: {welcome.get('data', {}).get('message', '')[:100]}...")
        except asyncio.TimeoutError:
            logger.warning("⚠️ 未收到欢迎消息")
        
        # 2. 发送简单聊天消息
        logger.info("📤 发送简单聊天消息...")
        chat_message = {
            "type": "chat",
            "data": {
                "message": "你好",
                "project_id": "285"
            }
        }
        
        await websocket.send(json.dumps(chat_message, ensure_ascii=False))
        logger.info("✅ 聊天消息已发送")
        
        # 3. 等待所有响应
        logger.info("⏳ 等待响应...")
        response_count = 0
        max_responses = 5  # 最多等待5个响应
        
        while response_count < max_responses:
            try:
                response_text = await asyncio.wait_for(websocket.recv(), timeout=30.0)
                response = json.loads(response_text)
                response_count += 1
                
                response_type = response.get("type")
                logger.info(f"📥 响应 {response_count}: 类型={response_type}")
                
                if response_type == "thinking":
                    logger.info("🤔 AI正在思考...")
                elif response_type == "chat_response":
                    logger.info("✅ 收到聊天响应!")
                    message_content = response.get("data", {}).get("message", "")
                    logger.info(f"💬 响应内容: {message_content[:200]}...")
                    break
                elif response_type == "error":
                    error_msg = response.get("data", {}).get("error", "")
                    logger.error(f"❌ 收到错误响应: {error_msg}")
                    break
                else:
                    logger.info(f"📋 其他响应: {json.dumps(response, ensure_ascii=False, indent=2)[:300]}...")
                    
            except asyncio.TimeoutError:
                logger.warning("⏰ 响应超时")
                break
            except Exception as e:
                logger.error(f"❌ 接收响应失败: {e}")
                break
        
        # 4. 测试ping
        logger.info("📤 发送ping测试...")
        ping_message = {
            "type": "ping",
            "data": {"timestamp": datetime.now().isoformat()}
        }
        
        await websocket.send(json.dumps(ping_message, ensure_ascii=False))
        
        try:
            pong_text = await asyncio.wait_for(websocket.recv(), timeout=10.0)
            pong = json.loads(pong_text)
            if pong.get("type") == "pong":
                logger.info("✅ Ping/Pong测试成功")
            else:
                logger.warning(f"⚠️ 收到非pong响应: {pong.get('type')}")
        except asyncio.TimeoutError:
            logger.warning("⏰ Ping响应超时")
        
        # 关闭连接
        await websocket.close()
        logger.info("🔚 连接已关闭")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        return False

async def main():
    """主函数"""
    print("🐛 WebSocket聊天功能调试测试")
    print("=" * 50)
    
    success = await debug_chat_test()
    
    if success:
        print("\n🎉 调试测试完成")
    else:
        print("\n❌ 调试测试失败")
    
    return success

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n🛑 测试被中断")
        exit(1)
