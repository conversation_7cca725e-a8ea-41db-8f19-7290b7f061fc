#!/bin/bash
# 连接问题修复脚本
# 适用于***********服务器

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
PROJECT_DIR="/data/agent"
MYSQL_HOST="***********"
MYSQL_PORT="3306"
MYSQL_USER="root"
MYSQL_PASSWORD="Spsm2021+"
MYSQL_DB="indusaio_agent"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否在正确的服务器上
check_server() {
    log_info "检查服务器环境..."
    
    # 检查IP地址
    local_ip=$(hostname -I | awk '{print $1}')
    if [[ "$local_ip" == "***********" ]]; then
        log_success "确认在目标服务器 *********** 上"
    else
        log_warning "当前服务器IP: $local_ip，不是目标服务器 ***********"
    fi
    
    # 检查主机名
    hostname=$(hostname)
    log_info "当前主机名: $hostname"
    
    if [[ "$hostname" == *"gateway"* ]]; then
        log_warning "检测到主机名包含'gateway'，这可能导致MySQL连接问题"
    fi
}

# 运行连接诊断
run_diagnosis() {
    log_info "运行连接诊断..."
    
    cd "$PROJECT_DIR"
    
    if [ -f "activate_env.sh" ]; then
        source activate_env.sh
        log_info "已激活Python环境"
    else
        log_warning "未找到activate_env.sh，使用系统Python"
    fi
    
    if [ -f "agents/predict_algorith_agent/scripts/diagnose_connection.py" ]; then
        python agents/predict_algorith_agent/scripts/diagnose_connection.py
    else
        log_error "诊断脚本不存在"
        return 1
    fi
}

# 检查MySQL服务状态
check_mysql_service() {
    log_info "检查MySQL服务状态..."
    
    # 检查MySQL进程
    if pgrep -x "mysqld" > /dev/null; then
        log_success "MySQL服务正在运行"
    else
        log_error "MySQL服务未运行"
        log_info "尝试启动MySQL服务..."
        
        # 尝试启动MySQL
        if systemctl start mysql 2>/dev/null || systemctl start mysqld 2>/dev/null; then
            log_success "MySQL服务启动成功"
        else
            log_error "无法启动MySQL服务，请手动检查"
            return 1
        fi
    fi
    
    # 检查端口监听
    if netstat -tlnp | grep ":3306 " > /dev/null; then
        log_success "MySQL端口3306正在监听"
    else
        log_error "MySQL端口3306未监听"
        return 1
    fi
}

# 检查Redis服务状态
check_redis_service() {
    log_info "检查Redis服务状态..."
    
    # 检查Redis进程
    if pgrep -x "redis-server" > /dev/null; then
        log_success "Redis服务正在运行"
    else
        log_error "Redis服务未运行"
        log_info "尝试启动Redis服务..."
        
        # 尝试启动Redis
        if systemctl start redis 2>/dev/null || systemctl start redis-server 2>/dev/null; then
            log_success "Redis服务启动成功"
        else
            log_error "无法启动Redis服务，请手动检查"
            return 1
        fi
    fi
    
    # 检查端口监听
    if netstat -tlnp | grep ":6379 " > /dev/null; then
        log_success "Redis端口6379正在监听"
    else
        log_error "Redis端口6379未监听"
        return 1
    fi
}

# 修复MySQL用户权限
fix_mysql_permissions() {
    log_info "修复MySQL用户权限..."

    # 获取当前主机信息
    hostname=$(hostname)
    local_ip=$(hostname -I | awk '{print $1}')

    log_info "当前主机名: $hostname"
    log_info "当前IP地址: $local_ip"

    # 创建SQL修复脚本
    cat > /tmp/fix_mysql_permissions.sql << EOF
-- 为当前主机名创建用户权限
CREATE USER IF NOT EXISTS 'root'@'$hostname' IDENTIFIED BY '$MYSQL_PASSWORD';
GRANT ALL PRIVILEGES ON *.* TO 'root'@'$hostname' WITH GRANT OPTION;

-- 为当前IP地址创建用户权限
CREATE USER IF NOT EXISTS 'root'@'$local_ip' IDENTIFIED BY '$MYSQL_PASSWORD';
GRANT ALL PRIVILEGES ON *.* TO 'root'@'$local_ip' WITH GRANT OPTION;

-- 为_gateway主机创建用户权限（关键修复）
CREATE USER IF NOT EXISTS 'root'@'_gateway' IDENTIFIED BY '$MYSQL_PASSWORD';
GRANT ALL PRIVILEGES ON *.* TO 'root'@'_gateway' WITH GRANT OPTION;

-- 为gateway主机创建用户权限（备用）
CREATE USER IF NOT EXISTS 'root'@'gateway' IDENTIFIED BY '$MYSQL_PASSWORD';
GRANT ALL PRIVILEGES ON *.* TO 'root'@'gateway' WITH GRANT OPTION;

-- 确保localhost权限存在
CREATE USER IF NOT EXISTS 'root'@'localhost' IDENTIFIED BY '$MYSQL_PASSWORD';
GRANT ALL PRIVILEGES ON *.* TO 'root'@'localhost' WITH GRANT OPTION;

-- 为通配符主机创建权限（最后的保险）
CREATE USER IF NOT EXISTS 'root'@'%' IDENTIFIED BY '$MYSQL_PASSWORD';
GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' WITH GRANT OPTION;

-- 刷新权限
FLUSH PRIVILEGES;

-- 显示所有root用户
SELECT User, Host FROM mysql.user WHERE User = 'root';
EOF

    # 执行SQL脚本
    log_info "执行MySQL权限修复..."
    if mysql -h localhost -u root -p"$MYSQL_PASSWORD" < /tmp/fix_mysql_permissions.sql; then
        log_success "MySQL权限修复完成"
    else
        log_error "MySQL权限修复失败"
        log_info "请手动执行以下命令:"
        log_info "mysql -h localhost -u root -p"
        log_info "然后执行 /tmp/fix_mysql_permissions.sql 中的SQL语句"
        return 1
    fi
    
    # 清理临时文件
    rm -f /tmp/fix_mysql_permissions.sql
}

# 测试连接
test_connections() {
    log_info "测试数据库连接..."
    
    cd "$PROJECT_DIR"
    
    if [ -f "activate_env.sh" ]; then
        source activate_env.sh
    fi
    
    # 创建简单的连接测试脚本
    cat > /tmp/test_connections.py << 'EOF'
import sys
sys.path.insert(0, '/data/agent')

try:
    from agents.config import MYSQL_HOST, MYSQL_PORT, MYSQL_USER, MYSQL_PASSWORD, MYSQL_DB
    import pymysql
    
    print("测试MySQL连接...")
    conn = pymysql.connect(
        host=MYSQL_HOST,
        port=MYSQL_PORT,
        user=MYSQL_USER,
        password=MYSQL_PASSWORD,
        database=MYSQL_DB,
        charset='utf8mb4',
        ssl_disabled=True
    )
    print("✅ MySQL连接成功")
    conn.close()
    
except Exception as e:
    print(f"❌ MySQL连接失败: {e}")

try:
    from agents.config import REDIS_HOST, REDIS_PORT, REDIS_PASSWORD, REDIS_DB
    import redis
    
    print("测试Redis连接...")
    r = redis.Redis(
        host=REDIS_HOST,
        port=REDIS_PORT,
        password=REDIS_PASSWORD,
        db=REDIS_DB,
        decode_responses=True
    )
    r.ping()
    print("✅ Redis连接成功")
    
except Exception as e:
    print(f"❌ Redis连接失败: {e}")
EOF

    python /tmp/test_connections.py
    rm -f /tmp/test_connections.py
}

# 重启服务
restart_service() {
    log_info "重启算法智能体服务..."
    
    cd "$PROJECT_DIR"
    
    # 停止现有服务
    if [ -f "manage_server.sh" ]; then
        ./manage_server.sh stop
        sleep 3
        ./manage_server.sh start
    else
        log_error "管理脚本不存在"
        return 1
    fi
}

# 显示使用帮助
show_help() {
    echo "🔧 算法智能体连接问题修复工具"
    echo "适用于***********服务器"
    echo ""
    echo "用法: $0 {check|diagnose|fix-mysql|fix-all|test|restart|help}"
    echo ""
    echo "命令说明:"
    echo "  check      - 检查服务器环境和服务状态"
    echo "  diagnose   - 运行完整的连接诊断"
    echo "  fix-mysql  - 修复MySQL用户权限问题"
    echo "  fix-all    - 执行所有修复操作"
    echo "  test       - 测试数据库连接"
    echo "  restart    - 重启算法智能体服务"
    echo "  help       - 显示此帮助信息"
}

# 主函数
main() {
    case "$1" in
        check)
            check_server
            check_mysql_service
            check_redis_service
            ;;
        diagnose)
            run_diagnosis
            ;;
        fix-mysql)
            fix_mysql_permissions
            ;;
        fix-all)
            log_info "开始执行完整修复流程..."
            check_server
            check_mysql_service
            check_redis_service
            fix_mysql_permissions
            test_connections
            restart_service
            log_success "修复流程完成！"
            ;;
        test)
            test_connections
            ;;
        restart)
            restart_service
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
