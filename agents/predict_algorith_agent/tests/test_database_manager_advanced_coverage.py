#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库管理器高级测试覆盖率
专门测试database/database_manager.py模块的高级功能和边界条件
"""

from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock

import pymysql
import pytest

# 导入待测试的模块
from agents.predict_algorith_agent.database.database_manager import DatabaseManager


class TestDatabaseManagerAdvanced:
    """数据库管理器高级功能测试"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        self.db_manager = DatabaseManager()
        
    def test_connection_config_properties(self):
        """测试连接配置属性"""
        assert hasattr(self.db_manager, 'connection_config')
        assert hasattr(self.db_manager, 'fallback_config')
        assert hasattr(self.db_manager, 'local_config')
        
        # 检查配置包含必要的键
        assert 'host' in self.db_manager.connection_config
        assert 'port' in self.db_manager.connection_config
        assert 'user' in self.db_manager.connection_config
        assert 'database' in self.db_manager.connection_config
        
    def test_get_connection_context_manager(self):
        """测试连接上下文管理器"""
        # 模拟连接成功
        mock_connection = MagicMock()
        
        with patch('pymysql.connect', return_value=mock_connection):
            with self.db_manager.get_connection() as conn:
                assert conn == mock_connection
                
        # 验证连接被正确关闭
        mock_connection.close.assert_called_once()
        
    def test_get_connection_retry_mechanism(self):
        """测试连接重试机制"""
        # 模拟前两次连接失败，第三次成功
        mock_connection = MagicMock()
        side_effects = [
            pymysql.Error("Connection failed"),
            pymysql.Error("Connection failed again"),
            mock_connection
        ]
        
        with patch('pymysql.connect', side_effect=side_effects):
            with patch('time.sleep'):  # 跳过实际的延迟
                with self.db_manager.get_connection(max_retries=3) as conn:
                    assert conn == mock_connection
                    
    def test_get_connection_all_configs_fail(self):
        """测试所有配置都失败的情况"""
        with patch('pymysql.connect', side_effect=pymysql.Error("All connections failed")):
            with patch('time.sleep'):
                with pytest.raises(Exception):
                    with self.db_manager.get_connection(max_retries=1):
                        pass
                        
    def test_create_conversation_with_mock(self):
        """测试创建对话（使用模拟）"""
        mock_connection = MagicMock()
        mock_cursor = MagicMock()
        mock_connection.cursor.return_value = mock_cursor
        
        conversation_data = {
            'user_id': 'test_user',
            'title': 'Test Conversation'
        }

        with patch.object(self.db_manager, 'get_connection') as mock_get_conn:
            mock_get_conn.return_value.__enter__.return_value = mock_connection

            result = self.db_manager.create_conversation(**conversation_data)
            
            # 验证SQL执行
            mock_cursor.execute.assert_called()
            assert result is True
            
    def test_get_conversation_with_mock(self):
        """测试获取对话（使用模拟）"""
        mock_connection = MagicMock()
        mock_cursor = MagicMock()
        mock_connection.cursor.return_value = mock_cursor
        
        # 模拟查询结果
        mock_cursor.fetchone.return_value = {
            'conversation_id': 'test_conv_id',
            'user_id': 'test_user',
            'title': 'Test Conversation',
            'status': 'active',
            'created_at': datetime.now()
        }
        
        with patch.object(self.db_manager, 'get_connection') as mock_get_conn:
            mock_get_conn.return_value.__enter__.return_value = mock_connection
            
            result = self.db_manager.get_conversation_detail('test_conv_id')
            
            assert result is not None
            assert result['conversation_id'] == 'test_conv_id'
            mock_cursor.execute.assert_called()
            
    def test_update_conversation_with_mock(self):
        """测试更新对话（使用模拟）"""
        mock_connection = MagicMock()
        mock_cursor = MagicMock()
        mock_connection.cursor.return_value = mock_cursor
        mock_cursor.rowcount = 1
        
        update_data = {
            'title': 'Updated Title',
            'status': 'completed',
            'progress': 100
        }
        
        with patch.object(self.db_manager, 'get_connection') as mock_get_conn:
            mock_get_conn.return_value.__enter__.return_value = mock_connection
            
            result = self.db_manager.update_conversation('test_conv_id', **update_data)
            
            assert result is True
            mock_cursor.execute.assert_called()
            
    def test_add_message_with_mock(self):
        """测试添加消息（使用模拟）"""
        mock_connection = MagicMock()
        mock_cursor = MagicMock()
        mock_connection.cursor.return_value = mock_cursor
        
        # 模拟获取下一个序号
        mock_cursor.fetchone.return_value = {'max_sequence': 5}
        
        with patch.object(self.db_manager, 'get_connection') as mock_get_conn:
            mock_get_conn.return_value.__enter__.return_value = mock_connection
            
            result = self.db_manager.add_conversation_message(
                conversation_id='test_conv_id',
                message_type='user',
                content='Test message'
            )
            
            assert result is True
            # 验证执行了两次SQL（获取序号 + 插入消息）
            assert mock_cursor.execute.call_count >= 2
            
    def test_get_conversation_messages_with_mock(self):
        """测试获取对话消息（使用模拟）"""
        mock_connection = MagicMock()
        mock_cursor = MagicMock()
        mock_connection.cursor.return_value = mock_cursor
        
        # 模拟消息列表
        mock_messages = [
            {
                'message_id': 1,
                'message_type': 'user',
                'content': 'Hello',
                'timestamp': datetime.now(),
                'message_sequence': 1
            },
            {
                'message_id': 2,
                'message_type': 'assistant',
                'content': 'Hi there!',
                'timestamp': datetime.now(),
                'message_sequence': 2
            }
        ]
        mock_cursor.fetchall.return_value = mock_messages
        
        with patch.object(self.db_manager, 'get_connection') as mock_get_conn:
            mock_get_conn.return_value.__enter__.return_value = mock_connection
            
            result = self.db_manager.get_conversation_messages('test_conv_id', limit=10)
            
            assert len(result) == 2
            assert result[0]['content'] == 'Hello'
            assert result[1]['content'] == 'Hi there!'
            
    def test_get_user_conversations_with_mock(self):
        """测试获取用户对话列表（使用模拟）"""
        mock_connection = MagicMock()
        mock_cursor = MagicMock()
        mock_connection.cursor.return_value = mock_cursor
        
        mock_conversations = [
            {
                'conversation_id': 'conv1',
                'title': 'Conversation 1',
                'status': 'active',
                'created_at': datetime.now()
            },
            {
                'conversation_id': 'conv2',
                'title': 'Conversation 2',
                'status': 'completed',
                'created_at': datetime.now() - timedelta(days=1)
            }
        ]
        mock_cursor.fetchall.return_value = mock_conversations
        
        with patch.object(self.db_manager, 'get_connection') as mock_get_conn:
            mock_get_conn.return_value.__enter__.return_value = mock_connection
            
            result = self.db_manager.get_user_conversations('test_user', limit=10)
            
            assert len(result) == 2
            assert result[0]['conversation_id'] == 'conv1'
            assert result[1]['conversation_id'] == 'conv2'
            
    def test_delete_conversation_with_mock(self):
        """测试删除对话（使用模拟）"""
        mock_connection = MagicMock()
        mock_cursor = MagicMock()
        mock_connection.cursor.return_value = mock_cursor
        mock_cursor.rowcount = 1
        
        with patch.object(self.db_manager, 'get_connection') as mock_get_conn:
            mock_get_conn.return_value.__enter__.return_value = mock_connection
            
            result = self.db_manager.delete_conversation('test_conv_id')
            
            assert result is True
            # 验证执行了删除消息和删除对话的SQL
            assert mock_cursor.execute.call_count >= 2
            
    def test_get_next_message_sequence_with_mock(self):
        """测试获取下一个消息序号（使用模拟）"""
        mock_connection = MagicMock()
        mock_cursor = MagicMock()
        mock_connection.cursor.return_value = mock_cursor
        
        # 测试有消息的情况
        mock_cursor.fetchone.return_value = {'max_sequence': 5}
        
        with patch.object(self.db_manager, 'get_connection') as mock_get_conn:
            mock_get_conn.return_value.__enter__.return_value = mock_connection
            
            result = self.db_manager.get_next_message_sequence('test_conv_id')
            
            assert result == 6
            
        # 测试没有消息的情况
        mock_cursor.fetchone.return_value = {'max_sequence': None}
        
        with patch.object(self.db_manager, 'get_connection') as mock_get_conn:
            mock_get_conn.return_value.__enter__.return_value = mock_connection
            
            result = self.db_manager.get_next_message_sequence('test_conv_id')
            
            assert result == 1
            
    def test_update_conversation_context_tokens_with_mock(self):
        """测试更新对话上下文token数量（使用模拟）"""
        mock_connection = MagicMock()
        mock_cursor = MagicMock()
        mock_connection.cursor.return_value = mock_cursor
        mock_cursor.rowcount = 1
        
        with patch.object(self.db_manager, 'get_connection') as mock_get_conn:
            mock_get_conn.return_value.__enter__.return_value = mock_connection
            
            result = self.db_manager.update_conversation_context_tokens('test_conv_id', 1500)
            
            assert result is True
            mock_cursor.execute.assert_called()
            
    def test_error_handling_in_operations(self):
        """测试操作中的错误处理"""
        # 测试数据库连接错误
        with patch.object(self.db_manager, 'get_connection', side_effect=Exception("DB Error")):
            result = self.db_manager.create_conversation(
                user_id='test',
                title='test'
            )
            assert result is False
            
        # 测试SQL执行错误
        mock_connection = MagicMock()
        mock_cursor = MagicMock()
        mock_connection.cursor.return_value = mock_cursor
        mock_cursor.execute.side_effect = pymysql.Error("SQL Error")
        
        with patch.object(self.db_manager, 'get_connection') as mock_get_conn:
            mock_get_conn.return_value.__enter__.return_value = mock_connection
            
            result = self.db_manager.get_conversation_detail('test_conv_id')
            assert result is None
            
    def test_connection_config_validation(self):
        """测试连接配置验证"""
        # 检查必要的配置项
        required_keys = ['host', 'port', 'user', 'password', 'database', 'charset']
        
        for key in required_keys:
            assert key in self.db_manager.connection_config
            
        # 检查端口是整数
        assert isinstance(self.db_manager.connection_config['port'], int)
        
        # 检查超时设置
        assert 'connect_timeout' in self.db_manager.connection_config
        assert isinstance(self.db_manager.connection_config['connect_timeout'], int)


class TestDatabaseManagerEdgeCases:
    """数据库管理器边界情况测试"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        self.db_manager = DatabaseManager()
        
    def test_empty_conversation_id(self):
        """测试空对话ID"""
        with patch.object(self.db_manager, 'get_connection'):
            result = self.db_manager.get_conversation_detail('')
            assert result is None

    def test_none_conversation_id(self):
        """测试None对话ID"""
        with patch.object(self.db_manager, 'get_connection'):
            result = self.db_manager.get_conversation_detail(None)
            assert result is None
            
    def test_very_long_content(self):
        """测试非常长的内容"""
        long_content = 'x' * 10000  # 10KB的内容
        
        mock_connection = MagicMock()
        mock_cursor = MagicMock()
        mock_connection.cursor.return_value = mock_cursor
        mock_cursor.fetchone.return_value = {'max_sequence': 0}
        
        with patch.object(self.db_manager, 'get_connection') as mock_get_conn:
            mock_get_conn.return_value.__enter__.return_value = mock_connection
            
            result = self.db_manager.add_conversation_message(
                conversation_id='test_conv_id',
                message_type='user',
                content=long_content
            )
            
            assert result is True
            
    def test_special_characters_in_content(self):
        """测试内容中的特殊字符"""
        special_content = "Test with 'quotes' and \"double quotes\" and \n newlines \t tabs"
        
        mock_connection = MagicMock()
        mock_cursor = MagicMock()
        mock_connection.cursor.return_value = mock_cursor
        mock_cursor.fetchone.return_value = {'max_sequence': 0}
        
        with patch.object(self.db_manager, 'get_connection') as mock_get_conn:
            mock_get_conn.return_value.__enter__.return_value = mock_connection
            
            result = self.db_manager.add_conversation_message(
                conversation_id='test_conv_id',
                message_type='user',
                content=special_content
            )
            
            assert result is True
            
    def test_json_data_handling(self):
        """测试JSON数据处理"""
        json_data = {
            'key1': 'value1',
            'key2': ['item1', 'item2'],
            'key3': {'nested': 'object'}
        }
        
        mock_connection = MagicMock()
        mock_cursor = MagicMock()
        mock_connection.cursor.return_value = mock_cursor
        mock_cursor.fetchone.return_value = {'max_sequence': 0}
        
        with patch.object(self.db_manager, 'get_connection') as mock_get_conn:
            mock_get_conn.return_value.__enter__.return_value = mock_connection
            
            result = self.db_manager.add_conversation_message(
                conversation_id='test_conv_id',
                message_type='user',
                content='Test message',
                message_data=json_data
            )
            
            assert result is True


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
