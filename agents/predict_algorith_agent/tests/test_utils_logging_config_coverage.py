#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Utils模块日志配置测试覆盖率
专门测试utils/logging_config.py模块的各种功能
"""

import logging
import os
from io import StringIO
from unittest.mock import patch

import pytest

# 导入待测试的模块
from agents.predict_algorith_agent.utils.logging_config import (
    ColoredFormatter, setup_logging, load_env_config,
    get_main_logger, get_agent_logger, get_database_logger
)


class TestColoredFormatter:
    """测试彩色日志格式化器"""
    
    def test_colored_formatter_init(self):
        """测试彩色格式化器初始化"""
        formatter = ColoredFormatter()
        assert formatter is not None
        assert hasattr(formatter, 'COLORS')
        assert 'DEBUG' in formatter.COLORS
        assert 'INFO' in formatter.COLORS
        assert 'ERROR' in formatter.COLORS
        
    def test_format_with_colors(self):
        """测试带颜色的格式化"""
        formatter = ColoredFormatter("%(levelname)s - %(message)s")
        
        # 创建测试记录
        record = logging.LogRecord(
            name="test", level=logging.INFO, pathname="", lineno=0,
            msg="Test message", args=(), exc_info=None
        )
        
        # 模拟终端环境
        with patch.dict(os.environ, {'ENABLE_COLOR_LOGGING': 'true'}):
            with patch('sys.stderr.isatty', return_value=True):
                formatted = formatter.format(record)
                assert "Test message" in formatted
                
    def test_format_without_colors(self):
        """测试不带颜色的格式化"""
        formatter = ColoredFormatter("%(levelname)s - %(message)s")
        
        record = logging.LogRecord(
            name="test", level=logging.INFO, pathname="", lineno=0,
            msg="Test message", args=(), exc_info=None
        )
        
        # 模拟非终端环境
        with patch.dict(os.environ, {'ENABLE_COLOR_LOGGING': 'false'}):
            formatted = formatter.format(record)
            assert "Test message" in formatted
            assert '\033[' not in formatted  # 不应包含ANSI颜色代码


class TestLoggingSetup:
    """测试日志设置功能"""
    
    def test_setup_logging_basic(self):
        """测试基本日志设置"""
        logger = setup_logging("test_logger")
        assert logger is not None
        assert logger.name == "test_logger"
        assert isinstance(logger, logging.Logger)
        
    def test_setup_logging_with_level(self):
        """测试指定级别的日志设置"""
        logger = setup_logging("test_logger", level="DEBUG")
        assert logger.level == logging.DEBUG
        
        logger = setup_logging("test_logger", level="ERROR")
        assert logger.level == logging.ERROR
        
    def test_setup_logging_with_custom_format(self):
        """测试自定义格式的日志设置"""
        custom_format = "%(name)s - %(message)s"
        logger = setup_logging("test_logger", format_string=custom_format)
        assert logger is not None
        
    def test_setup_logging_with_colors(self):
        """测试带颜色的日志设置"""
        logger = setup_logging("test_logger", use_colors=True)
        assert logger is not None
        
        logger = setup_logging("test_logger", use_colors=False)
        assert logger is not None


class TestEnvironmentConfig:
    """测试环境配置加载"""
    
    def test_load_env_config_defaults(self):
        """测试默认环境配置"""
        with patch.dict(os.environ, {}, clear=True):
            config = load_env_config()
            assert config is not None
            assert 'log_level' in config
            assert 'enable_file_logging' in config
            assert 'enable_color_logging' in config
            
    def test_load_env_config_custom(self):
        """测试自定义环境配置"""
        env_vars = {
            'LOG_LEVEL': 'DEBUG',
            'ENABLE_FILE_LOGGING': 'true',
            'ENABLE_COLOR_LOGGING': 'false',
            'LOG_FILE_PATH': '/tmp/test.log'
        }
        
        with patch.dict(os.environ, env_vars):
            config = load_env_config()
            assert config['log_level'] == 'DEBUG'
            assert config['enable_file_logging'] is True
            assert config['enable_color_logging'] is False
            assert config['log_file_path'] == '/tmp/test.log'


class TestPredefinedLoggers:
    """测试预定义日志器"""

    def test_get_main_logger(self):
        """测试获取主应用日志器"""
        logger = get_main_logger()
        assert logger is not None
        # 检查logger是否为Logger实例或Mock对象
        assert hasattr(logger, 'name')
        assert hasattr(logger, 'level')

    def test_get_agent_logger(self):
        """测试获取智能体日志器"""
        logger = get_agent_logger()
        assert logger is not None
        assert hasattr(logger, 'name')
        assert hasattr(logger, 'level')

    def test_get_database_logger(self):
        """测试获取数据库日志器"""
        logger = get_database_logger()
        assert logger is not None
        assert hasattr(logger, 'name')
        assert hasattr(logger, 'level')

    def test_logger_level_customization(self):
        """测试日志器级别自定义"""
        with patch.dict(os.environ, {'MAIN_LOG_LEVEL': 'DEBUG'}):
            logger = get_main_logger()
            assert hasattr(logger, 'level')

        with patch.dict(os.environ, {'AGENT_LOG_LEVEL': 'ERROR'}):
            logger = get_agent_logger()
            assert hasattr(logger, 'level')


class TestLoggingIntegration:
    """测试日志集成功能"""
    
    def test_logging_output_capture(self):
        """测试日志输出捕获"""
        # 创建字符串流来捕获日志输出
        log_stream = StringIO()
        
        # 创建测试日志器
        logger = logging.getLogger("test_capture")
        logger.setLevel(logging.INFO)
        
        # 添加流处理器
        handler = logging.StreamHandler(log_stream)
        handler.setFormatter(logging.Formatter("%(levelname)s - %(message)s"))
        logger.addHandler(handler)
        
        # 记录测试消息
        logger.info("Test message")
        
        # 检查输出
        output = log_stream.getvalue()
        assert "INFO - Test message" in output
        
        # 清理
        logger.removeHandler(handler)
        
    def test_multiple_loggers(self):
        """测试多个日志器"""
        logger1 = setup_logging("logger1", level="INFO")
        logger2 = setup_logging("logger2", level="DEBUG")
        
        assert logger1.name == "logger1"
        assert logger2.name == "logger2"
        assert logger1.level == logging.INFO
        assert logger2.level == logging.DEBUG
        
    def test_logger_hierarchy(self):
        """测试日志器层次结构"""
        parent_logger = setup_logging("parent")
        child_logger = setup_logging("parent.child")
        
        assert child_logger.parent == parent_logger or child_logger.parent.name == "parent"


class TestErrorHandling:
    """测试错误处理"""
    
    def test_invalid_log_level(self):
        """测试无效日志级别"""
        # 应该使用默认级别而不是抛出异常
        try:
            logger = setup_logging("test_logger", level="INVALID")
            assert logger is not None
        except AttributeError:
            # 预期的异常，测试通过
            pass
        
    def test_file_permission_error(self):
        """测试文件权限错误"""
        # 尝试写入只读目录
        readonly_path = "/root/readonly.log"  # 在Windows上可能不存在

        try:
            # 使用logging.FileHandler代替不存在的create_file_handler
            handler = logging.FileHandler(readonly_path)
            # 如果没有抛出异常，说明路径是可写的
            assert handler is not None
        except (PermissionError, FileNotFoundError, OSError):
            # 这是预期的行为
            pass
            
    def test_env_config_error_handling(self):
        """测试环境配置错误处理"""
        # 测试无效的布尔值
        with patch.dict(os.environ, {'ENABLE_FILE_LOGGING': 'invalid_bool'}):
            config = load_env_config()
            # 应该使用默认值
            assert isinstance(config['enable_file_logging'], bool)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
