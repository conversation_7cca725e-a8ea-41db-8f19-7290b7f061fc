#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
WebSocket管理器 - 处理前端UI对话框的实时交互
"""

import json
import asyncio
import logging
from typing import Dict, Set, Optional, Any
from datetime import datetime
from fastapi import WebSocket, WebSocketDisconnect
from pydantic import BaseModel, Field

# 设置日志
logger = logging.getLogger(__name__)

class DateTimeEncoder(json.JSONEncoder):
    """自定义JSON编码器，处理datetime对象"""
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)

from agents.predict_algorith_agent.core.predictive_agent import PredictiveAlgorithmAssistant
from agents.predict_algorith_agent.database.database_manager import DatabaseManager
from agents.predict_algorith_agent.utils.context_manager import ContextManager
from agents.config import ENABLE_WELCOME_MESSAGE
from pydantic_ai import Agent

# 配置日志
logger = logging.getLogger(__name__)

class WebSocketMessage(BaseModel):
    """WebSocket消息模型"""
    type: str = Field(description="消息类型")
    data: Dict[str, Any] = Field(description="消息数据")
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())
    session_id: Optional[str] = Field(None, description="会话ID")

class WebSocketResponse(BaseModel):
    """WebSocket响应模型"""
    type: str = Field(description="响应类型")
    data: Dict[str, Any] = Field(description="响应数据")
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())
    session_id: Optional[str] = Field(None, description="会话ID")
    status: str = Field(default="success", description="响应状态")

class ConnectionManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        # 活跃连接：{session_id: websocket}
        self.active_connections: Dict[str, WebSocket] = {}
        # 用户会话状态：{session_id: session_info}
        self.user_sessions: Dict[str, Dict[str, Any]] = {}
        # 算法智能体实例
        self.agent = PredictiveAlgorithmAssistant()
        # 数据库管理器
        self.db_manager = DatabaseManager()
        # 上下文管理器
        self.context_manager = ContextManager()
        
    async def connect(self, websocket: WebSocket, session_id: str, user_info: Dict[str, Any] = None):
        """建立WebSocket连接并初始化会话"""
        # 检查是否已有相同session_id的连接
        if session_id in self.active_connections:
            logger.warning(f"检测到重复session_id连接，断开旧连接: session_id={session_id}")
            # 断开旧连接
            old_websocket = self.active_connections[session_id]
            try:
                await old_websocket.close()
            except Exception as e:
                logger.debug(f"关闭旧连接失败: {e}")
            # 清理旧连接
            self.disconnect(session_id)

        # 建立新连接
        await websocket.accept()
        self.active_connections[session_id] = websocket

        # 获取用户信息
        user_id = user_info.get("user_id") if user_info else session_id
        username = user_info.get("username") if user_info else f"user_{user_id}"

        # 初始化用户会话状态，但不创建对话
        # 对话将在用户首次发送消息或切换到历史对话时创建/恢复
        initial_state = {
            "connected_at": datetime.now().isoformat(),
            "user_id": user_id,
            "username": username,
            "session_status": "connected",
            "last_activity": datetime.now().isoformat()
        }

        self.user_sessions[session_id] = {
            "conversation_id": None,  # 不在连接时创建对话
            "user_id": user_id,
            "username": username,
            "state": initial_state,
            "task_id": None,
            "connected_at": datetime.now().isoformat(),
            "message_count": 0,
            "context": None
        }

        # 创建session_states表记录
        try:
            self.db_manager.save_session_state(
                user_id=user_id,
                session_id=session_id,
                conversation_id="",  # 暂时为空，等创建对话时更新
                task_id="",  # 暂时为空，等创建任务时更新
                state_data=initial_state
            )
            logger.info(f"WebSocket连接建立并创建session_states记录: session_id={session_id}, user_id={user_id}")
        except Exception as e:
            logger.error(f"创建session_states记录失败: {e}")
            # 数据库操作失败不影响WebSocket连接建立
            logger.warning("数据库操作失败，但WebSocket连接仍然建立")

        # 根据配置决定是否发送欢迎引导消息（不创建对话）
        if ENABLE_WELCOME_MESSAGE:
            welcome_message = "您好！欢迎使用算法生成智能体！\n\n请问有什么能帮您，我猜您想问：\n(1) 我想训练一个算法，我要怎么做?\n(2) 你能提供哪些帮助？\n\n请选择您感兴趣的问题，或者直接描述您的需求。\n\n功能: 智能算法推荐, 参数自动提取, 实时对话交互, 训练进度监控, 分析报告生成"

            welcome_response = WebSocketResponse(
                type="welcome",
                data={
                    "message": welcome_message,
                    "session_id": session_id,
                    "type": "welcome_guide",
                    "options": [
                        {"key": "1", "text": "我想训练一个算法，我要怎么做?"},
                        {"key": "2", "text": "你能提供哪些帮助？"}
                    ],
                    "features": [
                        "智能算法推荐",
                        "参数自动提取",
                        "实时对话交互",
                        "训练进度监控",
                        "分析报告生成"
                    ]
                },
                session_id=session_id
            )

            try:
                await self.send_personal_message(welcome_response.dict(), session_id)
                logger.info(f"欢迎消息发送成功: session_id={session_id}")
            except Exception as e:
                logger.error(f"发送欢迎消息失败: session_id={session_id}, error={e}")
                # 如果欢迎消息发送失败，不影响连接建立
        else:
            logger.info(f"欢迎消息已禁用，跳过发送: session_id={session_id}")

    async def restore_conversation(self, session_id: str, conversation_id: str):
        """恢复历史对话"""
        try:
            # 1. 更新会话信息
            if session_id in self.user_sessions:
                self.user_sessions[session_id]["conversation_id"] = conversation_id

            # 2. 重建上下文
            context = self.context_manager.build_context(conversation_id)
            self.user_sessions[session_id]["context"] = context

            # 3. 重建智能体状态
            agent_state = self.context_manager.rebuild_agent_state(conversation_id)
            self.user_sessions[session_id]["state"] = agent_state.dict()

            # 4. 获取历史消息
            messages = self.db_manager.get_conversation_messages_with_context(conversation_id)

            # 5. 发送历史消息给前端
            history_response = WebSocketResponse(
                type="conversation_restored",
                data={
                    "conversation_id": conversation_id,
                    "messages": [
                        {
                            "type": msg["message_type"],
                            "content": msg["content"],
                            "timestamp": msg["timestamp"].isoformat() if msg["timestamp"] else None,
                            "sequence": msg["message_sequence"]
                        }
                        for msg in messages
                    ],
                    "context_summary": context.get("context_layers", {}).get("conversation_summary", ""),
                    "total_messages": len(messages)
                },
                session_id=session_id
            )

            await self.send_personal_message(history_response.dict(), session_id)

            logger.info(f"对话恢复成功: session_id={session_id}, conversation_id={conversation_id}")
            return True

        except Exception as e:
            logger.error(f"对话恢复失败: session_id={session_id}, conversation_id={conversation_id}, error={e}")
            await self.send_error_response(session_id, f"对话恢复失败: {str(e)}")
            return False

    def disconnect(self, session_id: str):
        """断开WebSocket连接"""
        if session_id in self.active_connections:
            del self.active_connections[session_id]
        if session_id in self.user_sessions:
            del self.user_sessions[session_id]
        logger.info(f"WebSocket连接断开: session_id={session_id}")
    
    async def send_personal_message(self, message: Dict[str, Any], session_id: str):
        """发送个人消息"""
        if session_id not in self.active_connections:
            logger.warning(f"尝试向不存在的会话发送消息: session_id={session_id}")
            return

        websocket = self.active_connections[session_id]

        # 首先检查WebSocket是否仍然有效
        try:
            # 检查WebSocket是否已经关闭
            if hasattr(websocket, 'client_state') and hasattr(websocket.client_state, 'value'):
                if websocket.client_state.value in [2, 3]:  # CLOSING or CLOSED
                    logger.warning(f"WebSocket已关闭，清理连接: session_id={session_id}")
                    self.disconnect(session_id)
                    return
        except Exception as state_error:
            logger.debug(f"WebSocket状态检查失败: {state_error}")

        try:
            # 检查WebSocket连接状态 - 使用简单可靠的方法
            try:
                # 检查应用状态（数值检查，避免导入问题）
                if hasattr(websocket, 'application_state'):
                    # 3 通常表示 DISCONNECTED 状态
                    if websocket.application_state == 3:
                        logger.warning(f"WebSocket应用已断开: session_id={session_id}")
                        self.disconnect(session_id)
                        return

                # 检查客户端状态
                if hasattr(websocket, 'client_state'):
                    # 检查客户端状态值
                    if hasattr(websocket.client_state, 'name') and websocket.client_state.name == "DISCONNECTED":
                        logger.warning(f"WebSocket客户端已断开: session_id={session_id}")
                        self.disconnect(session_id)
                        return
                    elif hasattr(websocket.client_state, 'value') and websocket.client_state.value == 3:  # CLOSED
                        logger.warning(f"WebSocket客户端已关闭: session_id={session_id}")
                        self.disconnect(session_id)
                        return

            except Exception as state_check_error:
                # 状态检查失败，但不影响发送尝试
                logger.debug(f"WebSocket状态检查失败，继续尝试发送: {state_check_error}")

            # 尝试发送消息
            await websocket.send_text(
                json.dumps(message, ensure_ascii=False, cls=DateTimeEncoder)
            )
            logger.debug(f"消息发送成功: session_id={session_id}")

        except Exception as e:
            error_msg = str(e)
            logger.error(f"发送消息失败: session_id={session_id}, error={error_msg}")

            # 检查是否是WebSocket状态错误
            if any(keyword in error_msg for keyword in [
                "Need to call \"accept\" first",
                "WebSocket is not connected",
                "Connection is closed",
                "WebSocket connection is closed"
            ]):
                logger.warning(f"WebSocket连接已断开，清理连接: session_id={session_id}")
            else:
                logger.error(f"WebSocket发送失败，未知错误: {error_msg}")

            # 连接可能已断开，清理连接
            self.disconnect(session_id)
    
    async def broadcast(self, message: Dict[str, Any]):
        """广播消息给所有连接"""
        disconnected_sessions = []
        for session_id, websocket in self.active_connections.items():
            try:
                await websocket.send_text(
                    json.dumps(message, ensure_ascii=False, cls=DateTimeEncoder)
                )
            except Exception as e:
                logger.error(f"广播消息失败: session_id={session_id}, error={e}")
                disconnected_sessions.append(session_id)

        # 清理断开的连接
        for session_id in disconnected_sessions:
            self.disconnect(session_id)
    
    async def handle_message(self, websocket: WebSocket, session_id: str, message_data: Dict[str, Any]):
        """处理接收到的消息"""
        try:
            # 🔍 添加原始消息数据的调试日志
            logger.info(f"原始message_data: {json.dumps(message_data, ensure_ascii=False)}")

            # 解析消息
            message = WebSocketMessage(**message_data)

            # 🔍 添加解析后消息的调试日志
            logger.info(f"解析后message: {json.dumps(message.dict(), ensure_ascii=False)}")

            # 更新会话统计
            if session_id in self.user_sessions:
                self.user_sessions[session_id]["message_count"] += 1

            logger.info(f"收到消息: session_id={session_id}, type={message.type}")

            # 根据消息类型处理
            if message.type == "chat":
                await self.handle_chat_message(session_id, message)
            elif message.type == "api_call":
                # 🔍 在调用api_call处理前再次检查
                logger.info(f"调用api_call处理前，message.data: {message.data}")
                await self.handle_api_call_message(session_id, message)
            elif message.type == "switch_conversation":
                await self.handle_switch_conversation(session_id, message)
            elif message.type == "new_conversation":
                await self.handle_new_conversation(session_id, message)
            elif message.type == "get_conversations":
                await self.handle_get_conversations(session_id, message)
            elif message.type == "get_progress":
                await self.handle_progress_request(session_id, message)
            elif message.type == "generate_report":
                await self.handle_report_request(session_id, message)
            elif message.type == "get_history":
                await self.handle_history_request(session_id, message)
            elif message.type == "ping":
                await self.handle_ping(session_id, message)
            else:
                await self.send_error_response(session_id, f"未知消息类型: {message.type}")
                
        except Exception as e:
            logger.error(f"处理消息异常: session_id={session_id}, error={e}")
            await self.send_error_response(session_id, f"消息处理失败: {str(e)}")
    
    async def handle_chat_message(self, session_id: str, message: WebSocketMessage):
        """处理对话消息"""
        try:
            user_input = message.data.get("message", "")
            project_id = message.data.get("project_id")  # 从消息中提取项目ID

            # 获取用户会话状态
            session_info = self.user_sessions.get(session_id, {})
            conversation_id = session_info.get("conversation_id")
            current_state = session_info.get("state")
            user_id = session_info.get("user_id", session_id)

            # 如果没有对话ID，创建新对话
            if not conversation_id:
                conversation_id = self.db_manager.create_conversation(
                    user_id=user_id,
                    session_id=session_id,
                    title="新建算法训练任务"
                )
                # 更新会话信息
                self.user_sessions[session_id]["conversation_id"] = conversation_id

                # 更新session_states表中的conversation_id
                try:
                    current_state = self.user_sessions[session_id].get("state", {})
                    current_state.update({
                        "conversation_id": conversation_id,
                        "conversation_created_at": datetime.now().isoformat(),
                        "last_activity": datetime.now().isoformat()
                    })

                    self.db_manager.save_session_state(
                        user_id=user_id,
                        session_id=session_id,
                        conversation_id=conversation_id,
                        task_id="",  # 暂时为空
                        state_data=current_state
                    )
                    self.user_sessions[session_id]["state"] = current_state
                    logger.info(f"为会话 {session_id} 创建新对话并更新session_states: {conversation_id}")
                except Exception as e:
                    logger.error(f"更新session_states失败: {e}")
                    logger.info(f"为会话 {session_id} 创建新对话: {conversation_id}")

            # 存储用户消息到数据库
            self.db_manager.add_conversation_message(
                conversation_id=conversation_id,
                message_type="user",
                content=user_input,
                interaction_type="chat",
                user_id=user_id
            )

            # 发送"正在思考"状态
            thinking_response = WebSocketResponse(
                type="thinking",
                data={"message": "正在分析您的需求..."},
                session_id=session_id
            )
            await self.send_personal_message(thinking_response.dict(), session_id)

            # 调用算法智能体处理（使用异步版本，传入项目ID）
            result, new_state, task_id = await self.agent.process_user_input_async(user_input, current_state, project_id)

            # 更新会话状态
            self.user_sessions[session_id]["state"] = new_state
            self.user_sessions[session_id]["task_id"] = task_id

            # 构建响应
            ai_message = result.get("msg", "")
            response_data = {
                "message": ai_message,
                "task_id": task_id,
                "state": new_state,
                "has_error": "error" in result,
                "conversation_id": conversation_id
            }

            # 如果有错误，添加错误信息
            if "error" in result:
                response_data["error"] = result["error"]

            # 如果有参数信息，添加参数
            if "params" in result:
                response_data["params"] = result["params"]

            # 存储AI回复到数据库（移除不能序列化的字段）
            db_response_data = {k: v for k, v in response_data.items() if k != 'conversation_id'}
            self.db_manager.add_conversation_message(
                conversation_id=conversation_id,
                message_type="assistant",
                content=ai_message,
                message_data=db_response_data,
                interaction_type="chat",
                user_id=user_id
            )

            # 更新上下文（异步）
            try:
                context = self.context_manager.build_context(conversation_id)
                self.user_sessions[session_id]["context"] = context
            except Exception as ctx_error:
                logger.warning(f"更新上下文失败: {ctx_error}")

            # 发送响应
            chat_response = WebSocketResponse(
                type="chat_response",
                data=response_data,
                session_id=session_id
            )

            await self.send_personal_message(chat_response.dict(), session_id)

        except Exception as e:
            error_msg = str(e)
            logger.error(f"处理对话消息异常: {e}")

            # 针对不同错误类型提供更友好的错误信息
            if "Received empty model response" in error_msg:
                # 使用兜底回答系统
                from ..services.fallback_responses import fallback_manager
                user_input = message.data.get("message", "")
                fallback_response = fallback_manager.get_fallback_response(user_input)

                # 发送兜底回答而不是错误信息
                response = WebSocketResponse(
                    type="chat_response",
                    data=fallback_response,
                    session_id=session_id
                )
                await self.send_personal_message(response.dict(), session_id)
                return

            elif "timeout" in error_msg.lower():
                user_error_msg = "请求超时，请稍后重试"
            elif "connection" in error_msg.lower():
                user_error_msg = "网络连接异常，请检查网络后重试"
            else:
                user_error_msg = f"对话处理失败，请重新描述您的需求"

            await self.send_error_response(session_id, user_error_msg)
    
    async def handle_progress_request(self, session_id: str, message: WebSocketMessage):
        """处理进度查询请求"""
        try:
            task_id = message.data.get("task_id")
            if not task_id:
                await self.send_error_response(session_id, "缺少task_id参数")
                return
            
            # 这里可以查询实际的训练进度
            # 目前返回模拟数据
            progress_data = {
                "task_id": task_id,
                "status": "training",
                "progress": 75,
                "current_epoch": 75,
                "total_epochs": 100,
                "loss": 0.234,
                "accuracy": 0.892,
                "estimated_time_remaining": "5分钟"
            }
            
            progress_response = WebSocketResponse(
                type="progress_update",
                data=progress_data,
                session_id=session_id
            )
            
            await self.send_personal_message(progress_response.dict(), session_id)
            
        except Exception as e:
            logger.error(f"处理进度请求异常: {e}")
            await self.send_error_response(session_id, f"进度查询失败: {str(e)}")
    
    async def handle_report_request(self, session_id: str, message: WebSocketMessage):
        """处理报告生成请求"""
        try:
            task_id = message.data.get("task_id")
            if not task_id:
                await self.send_error_response(session_id, "缺少task_id参数")
                return
            
            # 发送"正在生成报告"状态
            generating_response = WebSocketResponse(
                type="generating_report",
                data={"message": "正在生成分析报告..."},
                session_id=session_id
            )
            await self.send_personal_message(generating_response.dict(), session_id)
            
            # 获取会话中的参数
            session_info = self.user_sessions.get(session_id, {})
            state = session_info.get("state", {})
            params = state.get("current_params", {}) if isinstance(state, dict) else {}
            
            # 生成报告
            html_report = self.agent.generate_report(task_id, params)
            pdf_path = self.agent.get_report_pdf(task_id, params)
            
            report_data = {
                "task_id": task_id,
                "html_report": html_report,
                "pdf_path": pdf_path,
                "generated_at": datetime.now().isoformat()
            }
            
            report_response = WebSocketResponse(
                type="report_generated",
                data=report_data,
                session_id=session_id
            )
            
            await self.send_personal_message(report_response.dict(), session_id)
            
        except Exception as e:
            logger.error(f"处理报告请求异常: {e}")
            await self.send_error_response(session_id, f"报告生成失败: {str(e)}")
    
    async def handle_history_request(self, session_id: str, message: WebSocketMessage):
        """处理历史记录请求"""
        try:
            user_id = message.data.get("user_id", session_id)
            
            # 查询对话历史
            conversations = self.db_manager.get_conversations(user_id=user_id, limit=10)
            
            history_data = {
                "conversations": conversations,
                "total_count": len(conversations)
            }
            
            history_response = WebSocketResponse(
                type="history_data",
                data=history_data,
                session_id=session_id
            )
            
            await self.send_personal_message(history_response.dict(), session_id)
            
        except Exception as e:
            logger.error(f"处理历史记录请求异常: {e}")
            await self.send_error_response(session_id, f"历史记录查询失败: {str(e)}")

    async def handle_switch_conversation(self, session_id: str, message: WebSocketMessage):
        """处理对话切换请求"""
        try:
            conversation_id = message.data.get("conversation_id")
            if not conversation_id:
                await self.send_error_response(session_id, "缺少conversation_id参数")
                return

            # 恢复对话
            success = await self.restore_conversation(session_id, conversation_id)

            if success:
                response = WebSocketResponse(
                    type="conversation_switched",
                    data={
                        "conversation_id": conversation_id,
                        "message": "对话切换成功"
                    },
                    session_id=session_id
                )
                await self.send_personal_message(response.dict(), session_id)

        except Exception as e:
            logger.error(f"处理对话切换异常: {e}")
            await self.send_error_response(session_id, f"对话切换失败: {str(e)}")

    async def handle_new_conversation(self, session_id: str, message: WebSocketMessage):
        """处理新建对话请求"""
        try:
            title = message.data.get("title", "新建算法训练任务")

            # 从会话信息中获取用户ID
            session_info = self.user_sessions.get(session_id, {})
            user_id = session_info.get("user_id", session_id)

            # 创建新对话
            conversation_id = self.db_manager.create_conversation(
                user_id=user_id,
                session_id=session_id,
                title=title
            )

            # 更新会话信息
            if session_id in self.user_sessions:
                self.user_sessions[session_id]["conversation_id"] = conversation_id
                # 重置智能体状态
                self.user_sessions[session_id]["state"] = {}
                self.user_sessions[session_id]["task_id"] = None

            # 发送新对话创建成功响应
            response = WebSocketResponse(
                type="new_conversation_created",
                data={
                    "conversation_id": conversation_id,
                    "title": title,
                    "message": "新对话创建成功"
                },
                session_id=session_id
            )
            await self.send_personal_message(response.dict(), session_id)

            # 发送欢迎消息
            await self._send_welcome_message_for_conversation(session_id, conversation_id)

            logger.info(f"新对话创建成功: session_id={session_id}, conversation_id={conversation_id}")

        except Exception as e:
            logger.error(f"处理新建对话异常: {e}")
            await self.send_error_response(session_id, f"新建对话失败: {str(e)}")

    async def handle_get_conversations(self, session_id: str, message: WebSocketMessage):
        """处理获取对话列表请求"""
        try:
            # 从会话信息中获取用户ID
            session_info = self.user_sessions.get(session_id, {})
            user_id = session_info.get("user_id") or message.data.get("user_id", session_id)
            limit = message.data.get("limit", 20)
            offset = message.data.get("offset", 0)

            logger.info(f"获取对话列表: session_id={session_id}, user_id={user_id}, limit={limit}, offset={offset}")

            # 获取对话列表（只返回当前用户的对话）
            conversations = self.db_manager.get_user_conversations(
                user_id=user_id,
                limit=limit,
                offset=offset
            )

            logger.info(f"用户 {user_id} 找到 {len(conversations)} 个对话记录")

            # 格式化对话列表
            formatted_conversations = []
            for conv in conversations:
                formatted_conversations.append({
                    "conversation_id": conv["conversation_id"],
                    "title": conv["title"],
                    "status": conv["status"],
                    "created_at": conv["created_at"].isoformat() if conv["created_at"] else None,
                    "last_message_at": conv["last_message_at"].isoformat() if conv["last_message_at"] else None,
                    "message_count": conv["message_count"],
                    "last_user_message": conv["last_user_message"]
                })

            response = WebSocketResponse(
                type="conversations_list",
                data={
                    "conversations": formatted_conversations,
                    "total": len(formatted_conversations)
                },
                session_id=session_id
            )

            await self.send_personal_message(response.dict(), session_id)

        except Exception as e:
            logger.error(f"处理对话列表请求异常: {e}")
            await self.send_error_response(session_id, f"获取对话列表失败: {str(e)}")

    async def handle_ping(self, session_id: str, message: WebSocketMessage):
        """处理心跳消息"""
        pong_response = WebSocketResponse(
            type="pong",
            data={"timestamp": datetime.now().isoformat()},
            session_id=session_id
        )
        await self.send_personal_message(pong_response.dict(), session_id)
    
    async def send_error_response(self, session_id: str, error_message: str):
        """发送错误响应"""
        # 首先检查连接是否存在
        if session_id not in self.active_connections:
            logger.warning(f"尝试向不存在的会话发送错误响应: session_id={session_id}")
            return

        try:
            error_response = WebSocketResponse(
                type="error",
                data={"error": error_message},
                session_id=session_id,
                status="error"
            )
            await self.send_personal_message(error_response.dict(), session_id)
        except Exception as e:
            error_str = str(e)
            logger.error(f"发送错误响应失败: session_id={session_id}, error={error_str}")

            # 如果是WebSocket状态错误，说明连接已断开
            if any(keyword in error_str for keyword in [
                "Need to call \"accept\" first",
                "WebSocket is not connected",
                "Connection is closed"
            ]):
                logger.info(f"WebSocket连接已断开，跳过错误响应发送: session_id={session_id}")

            # 清理连接
            self.disconnect(session_id)
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """获取连接统计信息"""
        return {
            "active_connections": len(self.active_connections),
            "total_sessions": len(self.user_sessions),
            "sessions": {
                session_id: {
                    "connected_at": info.get("connected_at"),
                    "message_count": info.get("message_count", 0),
                    "has_task": bool(info.get("task_id"))
                }
                for session_id, info in self.user_sessions.items()
            }
        }

    async def _send_welcome_message_for_conversation(self, session_id: str, conversation_id: str):
        """为新建对话发送欢迎消息"""
        # 根据配置决定是否发送欢迎消息
        if not ENABLE_WELCOME_MESSAGE:
            logger.info(f"欢迎消息已禁用，跳过新建对话欢迎消息: session_id={session_id}, conversation_id={conversation_id}")
            return

        welcome_message = "您好！欢迎使用算法生成智能体！\n\n请问有什么能帮您，我猜您想问：\n(1) 我想训练一个算法，我要怎么做?\n(2) 你能提供哪些帮助？\n\n请选择您感兴趣的问题，或者直接描述您的需求。\n\n功能: 智能算法推荐, 参数自动提取, 实时对话交互, 训练进度监控, 分析报告生成"

        welcome_response = WebSocketResponse(
            type="welcome",
            data={
                "message": welcome_message,
                "session_id": session_id,
                "conversation_id": conversation_id,
                "type": "welcome_guide",
                "options": [
                    {"key": "1", "text": "我想训练一个算法，我要怎么做?"},
                    {"key": "2", "text": "你能提供哪些帮助？"}
                ],
                "features": [
                    "智能算法推荐", "参数自动提取", "实时对话交互",
                    "训练进度监控", "分析报告生成"
                ]
            },
            session_id=session_id
        )

        # 存储欢迎消息到数据库
        session_info = self.user_sessions.get(session_id, {})
        user_id = session_info.get("user_id", session_id)

        self.db_manager.add_conversation_message(
            conversation_id=conversation_id,
            message_type="assistant",
            content=welcome_message,
            interaction_type="welcome_guide",
            user_id=user_id
        )

        await self.send_personal_message(welcome_response.dict(), session_id)

    async def handle_api_call_message(self, session_id: str, message: WebSocketMessage):
        """处理前端直传参数的API调用消息"""
        action = "unknown"  # 初始化action，防止异常处理时未定义
        try:
            # 打印完整的消息结构到日志（结构化JSON）
            message_json = json.dumps(message.dict(), ensure_ascii=False, indent=2, cls=DateTimeEncoder)
            logger.info(f"收到WebSocket消息 - session_id={session_id}:\n{message_json}")

            # 🔍 详细检查data字段内容
            logger.info(f"message.data内容: {message.data}")
            logger.info(f"message.data类型: {type(message.data)}")
            logger.info(f"message.data.keys(): {list(message.data.keys()) if isinstance(message.data, dict) else 'Not a dict'}")

            action = message.data.get("action")
            project_id = message.data.get("project_id")
            parameters = message.data.get("parameters", {})

            # 🔍 详细检查action字段
            logger.info(f"action原始值: {repr(action)}")
            logger.info(f"action类型: {type(action)}")
            logger.info(f"action是否在data中: {'action' in message.data}")

            logger.info(f"解析API调用参数: session_id={session_id}, action={action}, project_id={project_id}, parameters={parameters}")

            # 验证必要参数
            if not action:
                logger.error(f"🚨 API调用缺少action参数!")
                logger.error(f"  - session_id: {session_id}")
                logger.error(f"  - message.data: {message.data}")
                logger.error(f"  - action值: {repr(action)}")
                logger.error(f"  - 'action' in data: {'action' in message.data}")
                await self.send_api_response(session_id, "unknown", "error", {
                    "code": "MISSING_ACTION",
                    "message": f"缺少action参数，data内容: {message.data}"
                })
                return

            # 定义不需要project_id的接口列表
            actions_without_project_id = {
                "get_all_algorithms",  # 查询全部算法名称（返回所有项目的算法）
                "get_all_datasets"     # 查询全部数据集名称（返回所有项目的数据集）
            }

            # 只对需要project_id的接口进行验证
            if action not in actions_without_project_id and not project_id:
                await self.send_api_response(session_id, action, "error", {
                    "code": "MISSING_PROJECT_ID",
                    "message": "项目ID不能为空"
                })
                return

            # 执行API调用
            result = await self._execute_api_call(action, project_id, parameters)

            # 发送成功响应
            await self.send_api_response(session_id, action, "success", result)

        except Exception as e:
            logger.error(f"API调用处理失败: session_id={session_id}, error={e}")
            await self.send_api_response(session_id, action, "error", {
                "code": "API_CALL_ERROR",
                "message": str(e),
                "details": f"API调用执行失败: {str(e)}"
            })

    async def _execute_api_call(self, action: str, project_id: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """执行具体的API调用"""
        from ..services.algorithm_platform_service import AlgorithmPlatformService
        from ..models.algorithm_platform_models import AlgorithmTrainingRequest, MonitoringServiceRequest

        platform_service = AlgorithmPlatformService()

        if action == "train_algorithm":
            # 训练新算法
            request = AlgorithmTrainingRequest(
                parameter1=parameters.get("parameter1", ""),
                parameter2=parameters.get("parameter2", ""),
                parameter3=parameters.get("parameter3", ""),
                parameter4=parameters.get("parameter4", ""),
                parameter5=parameters.get("parameter5", ""),
                parameter6=parameters.get("parameter6", ""),
                parameter7=parameters.get("parameter7", ""),
                parameter8=parameters.get("parameter8", ""),
                parameter9=parameters.get("parameter9", ""),
                parameter10=parameters.get("parameter10", ""),
                parameter11=parameters.get("parameter11", ""),
                parameter12=parameters.get("parameter12", ""),
                parameter13=parameters.get("parameter13", ""),
                parameter14=parameters.get("parameter14", ""),
                parameter15=parameters.get("parameter15", ""),
                parameter16=parameters.get("parameter16", ""),
                project_number=project_id
            )
            response = await platform_service.train_algorithm(request)
            return response.dict()

        elif action == "start_monitoring_service":
            # 启动监控分析服务
            request = MonitoringServiceRequest(
                db_ip=parameters.get("db_ip", ""),
                db_name=parameters.get("db_name", ""),
                list_name=parameters.get("list_name", ""),
                implement_name=parameters.get("implement_name", ""),
                project_name=project_id
            )
            response = await platform_service.start_monitoring_service(request)
            return response.dict()

        elif action == "get_all_algorithms":
            # 查询全部算法名称（返回所有项目的算法）
            response = await platform_service.get_all_algorithms()
            return response.dict()

        elif action == "get_all_datasets":
            # 查询全部数据集名称（返回所有项目的数据集）
            response = await platform_service.get_all_datasets()
            return response.dict()

        elif action == "get_all_iot":
            # 查询全部IOT名称
            response = await platform_service.get_all_iot(project_id)
            return response.dict()

        elif action == "get_algorithm_log":
            # 查询算法训练信息
            algorithm_name = parameters.get("algorithm_name", "")
            if not algorithm_name:
                raise ValueError("algorithm_name参数不能为空")
            response = await platform_service.get_algorithm_log(algorithm_name, project_id)
            return response.dict()

        elif action == "get_all_services":
            # 查询全部服务名称
            response = await platform_service.get_all_services(project_id)
            return response.dict()

        elif action == "get_service_prediction":
            # 查询服务预测结果
            service_name = parameters.get("service_name", "")
            if not service_name:
                raise ValueError("service_name参数不能为空")
            response = await platform_service.get_service_prediction(service_name, project_id)
            return response.dict()

        else:
            raise ValueError(f"不支持的API调用: {action}")

    async def send_api_response(self, session_id: str, action: str, status: str, data: Dict[str, Any]):
        """发送API调用响应"""
        from datetime import datetime

        response = {
            "type": "api_response",
            "data": {
                "action": action,
                "status": status,
                "result" if status == "success" else "error": data,
                "timestamp": datetime.now().isoformat()
            }
        }

        await self.send_personal_message(response, session_id)

# 注意：不在这里创建全局实例，避免与websocket_routes.py中的实例冲突
# 全局实例应该在websocket_routes.py中统一管理
