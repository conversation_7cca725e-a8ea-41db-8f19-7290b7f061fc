#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
WebSocket路由模块 - 定义WebSocket端点和处理逻辑
"""

import json
import logging
import uuid
from datetime import datetime
from typing import Optional

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Query, HTTPException
from fastapi.responses import JSONResponse

# 导入WebSocket连接管理器
from agents.predict_algorith_agent.network.websocket_manager import ConnectionManager

# 配置日志
logger = logging.getLogger(__name__)

# 创建WebSocket路由器
router = APIRouter()

# 创建连接管理器实例
connection_manager = ConnectionManager()

@router.websocket("/ws/chat")
async def websocket_chat_endpoint(
    websocket: WebSocket,
    session_id: Optional[str] = Query(None, description="会话ID，如果不提供将自动生成"),
    user_id: Optional[str] = Query(None, description="用户ID，从web系统传入"),
    username: Optional[str] = Query(None, description="用户名，从web系统传入")
):
    """
    WebSocket聊天端点

    连接URL示例：
    ws://localhost:8008/api/ws/chat?session_id=user123&user_id=12345&username=张三

    消息格式：
    {
        "type": "chat",
        "data": {
            "message": "我想用LSTM做预测"
        }
    }
    """
    # 验证用户信息
    if not user_id:
        logger.warning(f"WebSocket连接缺少用户ID: session_id={session_id}")
        await websocket.close(code=4001, reason="Missing user_id")
        return

    # 如果没有提供session_id，自动生成一个32位UUID
    if not session_id:
        session_id = uuid.uuid4().hex

    logger.info(f"WebSocket连接请求: session_id={session_id}, user_id={user_id}, username={username}")

    try:
        # 建立连接，传递用户信息
        await connection_manager.connect(websocket, session_id, user_info={
            "user_id": user_id,
            "username": username or f"user_{user_id}"
        })
        
        # 消息循环
        while True:
            try:
                # 接收消息
                data = await websocket.receive_text()
                message_data = json.loads(data)
                
                # 处理消息
                await connection_manager.handle_message(websocket, session_id, message_data)
                
            except WebSocketDisconnect:
                logger.info(f"WebSocket正常断开: session_id={session_id}")
                break
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析错误: session_id={session_id}, error={e}")
                # JSON解析错误，不发送错误响应，直接跳过
                continue
            except Exception as e:
                logger.error(f"消息处理异常: session_id={session_id}, error={e}")
                # 消息处理异常，不尝试发送错误响应，避免状态问题
                # 直接清理连接并退出循环
                connection_manager.disconnect(session_id)
                break
                
    except Exception as e:
        logger.error(f"WebSocket连接异常: session_id={session_id}, error={e}")
    finally:
        # 清理连接
        connection_manager.disconnect(session_id)

@router.get("/ws/stats")
async def get_websocket_stats():
    """
    获取WebSocket连接统计信息（简化版）

    Returns:
        dict: 连接统计信息
    """
    try:
        # 简化版本，返回基础统计信息
        stats = {
            "active_connections": 0,
            "total_sessions": 0,
            "status": "simplified_mode"
        }
        return JSONResponse(content={
            "success": True,
            "data": stats,
            "message": "获取统计信息成功（简化模式）"
        })
    except Exception as e:
        logger.error(f"获取WebSocket统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")

@router.post("/ws/broadcast")
async def broadcast_message(message: dict):
    """
    广播消息给所有连接的客户端
    
    Args:
        message: 要广播的消息
        
    Returns:
        dict: 广播结果
    """
    try:
        await connection_manager.broadcast(message)
        return JSONResponse(content={
            "success": True,
            "message": "消息广播成功",
            "active_connections": len(connection_manager.active_connections)
        })
    except Exception as e:
        logger.error(f"广播消息失败: {e}")
        raise HTTPException(status_code=500, detail=f"广播消息失败: {str(e)}")

@router.post("/ws/session/{session_id}/disconnect")
async def disconnect_session(session_id: str):
    """
    强制断开指定会话
    
    Args:
        session_id: 会话ID
        
    Returns:
        dict: 断开结果
    """
    try:
        if session_id in connection_manager.active_connections:
            # 发送断开通知
            await connection_manager.send_personal_message({
                "type": "disconnect",
                "data": {"reason": "服务器主动断开连接"},
                "timestamp": datetime.now().isoformat()
            }, session_id)
            
            # 断开连接
            connection_manager.disconnect(session_id)
            
            return JSONResponse(content={
                "success": True,
                "message": f"会话 {session_id} 已断开"
            })
        else:
            return JSONResponse(content={
                "success": False,
                "message": f"会话 {session_id} 不存在或已断开"
            }, status_code=404)
            
    except Exception as e:
        logger.error(f"断开会话失败: {e}")
        raise HTTPException(status_code=500, detail=f"断开会话失败: {str(e)}")

@router.get("/ws/health")
async def websocket_health_check():
    """
    WebSocket服务健康检查（简化版）

    Returns:
        dict: 健康状态信息
    """
    try:
        return JSONResponse(content={
            "status": "healthy",
            "service": "WebSocket Chat Service (Simplified)",
            "active_connections": 0,
            "total_sessions": 0,
            "timestamp": datetime.now().isoformat(),
            "mode": "simplified"
        })
    except Exception as e:
        logger.error(f"WebSocket健康检查失败: {e}")
        return JSONResponse(content={
            "status": "unhealthy",
            "error": str(e)
        }, status_code=500)
